<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="Skèmino - Il gioco strategico che unisce carte e strategia. Gioca online o in locale contro amici e sfida giocatori da tutto il mondo.">
    <meta name="keywords" content="Skèmino, gioco di carte, gioco strategico, gioco online, gioco da tavolo">
    <meta name="theme-color" content="#172a45">
    <meta name="author" content="Skemino Development Team">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://skemino.it/">
    <meta property="og:title" content="Skèmino - Dashboard Giocatore">
    <meta property="og:description" content="Dashboard personale per i giocatori di Skèmino. Accedi alle tue statistiche, partite e tornei.">
    <meta property="og:image" content="img/carte/skemino.webp">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://skemino.it/">
    <meta property="twitter:title" content="Skèmino - Dashboard Giocatore">
    <meta property="twitter:description" content="Dashboard personale per i giocatori di Skèmino. Accedi alle tue statistiche, partite e tornei.">
    <meta property="twitter:image" content="img/carte/skemino.webp">

    <link rel="canonical" href="https://skemino.it/dashboard">
    <title>Skèmino - Dashboard Giocatore</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/multiplayer.css">
    <link rel="stylesheet" href="css/card-animations.css">
    <link rel="stylesheet" href="css/psn-unified.css">
    <link rel="stylesheet" href="css/board-snapshot.css">
    <link rel="stylesheet" href="css/page-transitions.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Precaricamento delle immagini critiche -->
    <link rel="preload" href="img/carte/Loop_card.webp" as="image" type="image/webp" fetchpriority="low">
    <link rel="preload" href="img/carte/card-back.webp" as="image" type="image/webp" fetchpriority="high">
    <link rel="preload" href="img/carte/skemino.webp" as="image" type="image/webp" fetchpriority="high">

    <style>
        /* Stili specifici per la dashboard giocatore */
        .dashboard-welcome {
            background-color: #1c3460;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .welcome-text {
            flex: 1;
        }

        .welcome-text h2 {
            margin-top: 0;
            font-size: 1.8rem;
        }

        .welcome-text p {
            margin-bottom: 0;
            opacity: 0.8;
        }

        .player-stats {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-card {
            background-color: #f5f8ff;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #172a45;
            display: block;
            margin: 10px 0;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        .player-avatar-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: #f0f0f0;
            overflow: hidden;
            margin-left: 20px;
            border: 3px solid white;
        }

        .recent-games {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .game-list {
            margin-top: 15px;
        }

        .game-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }

        .game-item:hover {
            background-color: #f5f8ff;
        }

        .game-item:last-child {
            border-bottom: none;
        }

        .game-result {
            width: 80px;
            text-align: center;
            font-weight: bold;
        }

        .game-result.win {
            color: #27ae60;
        }

        .game-result.loss {
            color: #e74c3c;
        }

        .game-result.draw {
            color: #f39c12;
        }

        .game-info {
            flex: 1;
            margin-left: 15px;
        }

        .game-opponent {
            font-weight: bold;
        }

        .game-date {
            font-size: 0.85rem;
            color: #666;
        }

        .game-actions {
            margin-left: 15px;
        }

        .game-actions button {
            background-color: #eee;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .game-actions button:hover {
            background-color: #ddd;
        }

        .dashboard-section-title {
            color: #172a45;
            margin-top: 0;
            display: flex;
            align-items: center;
        }

        .dashboard-section-title i {
            margin-right: 10px;
            color: #3498db;
        }

        /* Badge per il livello di abilità */
        .skill-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            background-color: #3498db;
            color: white;
            font-size: 0.8rem;
            margin-left: 10px;
            vertical-align: middle;
        }

        .no-games-message {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .dashboard-footer {
            text-align: center;
            margin-top: 30px;
            margin-bottom: 20px;
            color: #666;
            font-size: 0.9rem;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }

        .action-button {
            flex: 1;
            padding: 15px;
            text-align: center;
            background-color: #f5f8ff;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            color: #172a45;
            font-weight: bold;
        }

        .action-button i {
            display: block;
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #3498db;
        }

        .action-button:hover {
            background-color: #172a45;
            color: white;
        }

        .action-button:hover i {
            color: white;
        }

        .primary-action {
            background-color: #3498db;
            color: white;
        }

        .primary-action i {
            color: white;
        }

        .primary-action:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body class="loading">
    <!-- Precarica le immagini di copertura delle carte per evitare il flash durante l'animazione -->
    <div class="preload-card-backs" aria-hidden="true">
        <!-- Immagini precaricate con attributi ottimizzati -->
        <img src="img/carte/card-back.webp" alt="Card Back" class="card-back-image card-visible"
             loading="eager" fetchpriority="high" decoding="sync"
             draggable="false" style="transform: translateZ(0); -webkit-transform: translateZ(0);">

        <img src="/img/Cover carte/cover.png" alt="Card Back" class="card-back-image card-visible"
             loading="eager" fetchpriority="high" decoding="sync"
             draggable="false" style="transform: translateZ(0); -webkit-transform: translateZ(0);">
    </div>

    <div id="homepage">
        <!-- Barra laterale sinistra -->
        <div class="sidebar">
            <div class="logo">
                <img src="img/carte/skemino.webp" alt="Skèmino" class="logo-image">
            </div>
            <nav>
                <ul>
                    <li><i class="fas fa-play"></i> Gioca</li>
                    <li><i class="fas fa-book"></i> Regole</li>
                    <li><i class="fas fa-trophy"></i> Tornei</li>
                    <li><i class="fas fa-graduation-cap"></i> Tutorial</li>
                    <li class="active"><i class="fas fa-chart-line"></i> Statistiche</li>
                    <li><i class="fas fa-users"></i> Community</li>
                    <li id="classifica-link"><i class="fas fa-medal"></i> Classifica</li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <div id="auth-buttons" style="display: none;">
                    <button id="login-button" class="auth-btn"><i class="fas fa-sign-in-alt"></i> Accedi</button>
                    <button id="register-button" class="auth-btn"><i class="fas fa-user-plus"></i> Registrati</button>
                </div>
                <div id="user-profile">
                    <div class="user-info">
                        <span id="username-display"></span>
                        <span id="user-rank"></span>
                    </div>
                    <button id="logout-button" class="auth-btn"><i class="fas fa-sign-out-alt"></i> Esci</button>
                    <button id="fix-auth-button" class="auth-btn" style="display: none; margin-top: 5px; background-color: #e74c3c;"><i class="fas fa-wrench"></i> Ripara Accesso</button>
                </div>
                <div class="sidebar-controls">
                    <button class="lang-switch"><i class="fas fa-globe"></i> Italiano</button>
                    <button class="support-btn"><i class="fas fa-question-circle"></i> Supporto</button>
                </div>
            </div>
        </div>

        <!-- Contenuto principale -->
        <main class="main-content">
            <!-- Dashboard del giocatore -->
            <section class="dashboard-welcome">
                <div class="welcome-text">
                    <h2>Benvenuto, <span id="player-name">Giocatore</span>!</h2>
                    <p>Ecco la tua dashboard personale con statistiche, partite recenti e attività</p>
                </div>
                <div class="player-avatar-large" id="player-avatar-container">
                    <!-- L'avatar verrà inserito qui da JavaScript -->
                </div>
            </section>

            <section class="quick-actions">
                <button id="play-online-button" class="action-button primary-action">
                    <i class="fas fa-stopwatch"></i>
                    Gioca a 30 minuti
                </button>
                <button id="new-game-button" class="action-button">
                    <i class="fas fa-plus-circle"></i>
                    Nuova Partita
                </button>
                <button id="training-button" class="action-button">
                    <i class="fas fa-brain"></i>
                    Allenamento
                </button>
                <button id="start-local-button" class="action-button">
                    <i class="fas fa-user-friends"></i>
                    Sfida un Amico
                </button>
            </section>

            <section class="player-stats">
                <h3 class="dashboard-section-title">
                    <i class="fas fa-chart-line"></i>
                    Le tue statistiche
                    <span class="skill-badge" id="player-level-badge">Principiante</span>
                </h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <i class="fas fa-trophy"></i>
                        <span class="stat-value" id="stats-rating">1000</span>
                        <span class="stat-label">Rating</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-check-circle"></i>
                        <span class="stat-value" id="stats-wins">0</span>
                        <span class="stat-label">Vittorie</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-times-circle"></i>
                        <span class="stat-value" id="stats-losses">0</span>
                        <span class="stat-label">Sconfitte</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-handshake"></i>
                        <span class="stat-value" id="stats-draws">0</span>
                        <span class="stat-label">Pareggi</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-gamepad"></i>
                        <span class="stat-value" id="stats-games">0</span>
                        <span class="stat-label">Partite totali</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-percentage"></i>
                        <span class="stat-value" id="stats-winrate">0%</span>
                        <span class="stat-label">Percentuale vittorie</span>
                    </div>
                </div>
            </section>

            <section class="recent-games">
                <h3 class="dashboard-section-title">
                    <i class="fas fa-history"></i>
                    Partite recenti
                </h3>
                <div class="game-list" id="recent-games-list">
                    <!-- Qui verranno inserite le partite recenti tramite JavaScript -->
                    <div class="no-games-message">
                        <i class="fas fa-chess-board" style="font-size: 2rem; margin-bottom: 10px; color: #ddd;"></i>
                        <p>Non hai ancora giocato partite online. Inizia a giocare per vedere qui la tua cronologia!</p>
                    </div>

                    <!-- Esempio di struttura di una partita (commentato, sarà generato da JS) -->
                    <!--
                    <div class="game-item">
                        <div class="game-result win">Vittoria</div>
                        <div class="game-info">
                            <div class="game-opponent">vs. Avversario123</div>
                            <div class="game-date">16 maggio 2023, 14:30</div>
                        </div>
                        <div class="game-actions">
                            <button><i class="fas fa-search"></i> Analizza</button>
                        </div>
                    </div>
                    -->
                </div>
            </section>

            <!-- Sezione Features -->
            <h2 class="academy-title">Academy Skèmino</h2>
            <p class="academy-subtitle">Articoli e guide per migliorare il tuo gioco</p>

            <section class="features-section">
                <a href="#academy-strategia" class="feature-card">
                    <i class="fas fa-brain"></i>
                    <h3>Strategie Avanzate</h3>
                    <p>Scopri le migliori tattiche per dominare il tabellone</p>
                    <span class="read-more">Leggi l'articolo →</span>
                </a>
                <a href="#academy-principianti" class="feature-card">
                    <i class="fas fa-graduation-cap"></i>
                    <h3>Guida per Principianti</h3>
                    <p>Impara le basi e i consigli per iniziare alla grande</p>
                    <span class="read-more">Leggi l'articolo →</span>
                </a>
                <a href="#academy-tornei" class="feature-card">
                    <i class="fas fa-trophy"></i>
                    <h3>Tornei e Competizioni</h3>
                    <p>Tutto quello che devi sapere per partecipare ai tornei ufficiali</p>
                    <span class="read-more">Leggi l'articolo →</span>
                </a>
                <a href="#academy-varianti" class="feature-card">
                    <i class="fas fa-dice"></i>
                    <h3>Varianti di Gioco</h3>
                    <p>Scopri nuovi modi di giocare a Skèmino per divertirti ancora di più</p>
                    <span class="read-more">Leggi l'articolo →</span>
                </a>
            </section>

            <div class="dashboard-footer">
                <p>Skèmino - Versione 1.0.0 - © 2023 Skèmino Development Team</p>
            </div>
        </main>
    </div>

    <!-- Elementi originali del gioco, nascosti inizialmente -->
    <div id="main-menu" style="display: none;"></div>

    <!-- Schermata inserimento nomi giocatori -->
    <div id="player-names-screen" style="display: none;">
        <div class="names-container">
            <h2>Inserisci i nomi dei giocatori</h2>
            <p class="names-info">Il Giocatore G1 (bianco) inizia sempre la partita. Il dado colore determinerà quale giocatore sarà G1.</p>
            <div class="player-name-input">
                <label for="player1-name">
                    <i class="fas fa-user"></i> Giocatore 1
                </label>
                <input type="text" id="player1-name" maxlength="20" placeholder="Nome Giocatore 1">
            </div>
            <div class="player-name-input">
                <label for="player2-name">
                    <i class="fas fa-user"></i> Giocatore 2
                </label>
                <input type="text" id="player2-name" maxlength="20" placeholder="Nome Giocatore 2">
            </div>
            <p id="names-error-message" class="error-message"></p>
            <div class="names-buttons">
                <button id="confirm-names-button" class="primary-btn">
                    <i class="fas fa-check"></i> Conferma
                </button>
                <button id="back-to-menu-button" class="secondary-btn">
                    <i class="fas fa-arrow-left"></i> Indietro
                </button>
            </div>
        </div>
    </div>

    <div id="setup-animation" style="display: none;">
        <h2>Preparazione Partita...</h2>
        <div id="dice-area">
            <!-- I dadi appariranno qui -->
        </div>
        <p id="dice-result-text">Lancio dei dadi...</p>
    </div>

    <!-- Riquadro di vittoria -->
    <div id="victory-screen" style="display: none;">
        <div class="victory-container">
            <div class="victory-header">
                <i class="fas fa-trophy victory-icon"></i>
                <h2>Vittoria!</h2>
            </div>
            <div class="victory-content">
                <p id="winner-name">Nome Vincitore</p>
                <div id="rating-changes">
                    <p id="winner-rating" class="rating-change winner-rating">Rating: <span class="old-rating"></span> → <span class="new-rating"></span> <span class="rating-diff"></span></p>
                    <p id="loser-rating" class="rating-change loser-rating">Rating: <span class="old-rating"></span> → <span class="new-rating"></span> <span class="rating-diff"></span></p>
                </div>
                <p id="victory-reason">Motivo della vittoria</p>
            </div>
            <div class="victory-buttons">
                <button id="examine-game-victory" class="primary-btn">
                    <i class="fas fa-search"></i> Esamina Partita
                </button>
                <button id="new-game-victory" class="secondary-btn">
                    <i class="fas fa-redo"></i> Nuova Partita
                </button>
            </div>
        </div>
    </div>

    <!-- Overlay per l'animazione di inizio battaglia -->
    <div id="battle-start-overlay">
        <div class="battle-start-text"></div>
        <div class="battle-particles"></div>
    </div>

    <!-- Matchmaking Modal -->
    <div id="matchmaking-modal" class="matchmaking-modal">
        <div class="matchmaking-content">
            <h2>Ricerca Avversario</h2>
            <div class="matchmaking-spinner"></div>
            <p id="matchmaking-status" class="matchmaking-status">Ricerca di un avversario in corso...</p>
            <div class="matchmaking-buttons">
                <button id="cancel-matchmaking" class="cancel-matchmaking"><i class="fas fa-times-circle"></i> Annulla Ricerca</button>
            </div>
        </div>
    </div>

    <!-- Turn Timer -->
    <div id="turn-timer-container" class="turn-timer-container" style="display: none;">
        <div id="turn-timer" class="turn-timer">00:00</div>
    </div>

    <!-- Notifications -->
    <div id="notification-container" class="notification-container"></div>

    <!-- Classifica -->
    <div id="classifica-screen" style="display: none;">
        <!-- Contenuto della classifica rimane invariato -->
        <div class="classifica-container">
            <div class="classifica-header">
                <h2>Classifica dei Giocatori</h2>
                <p>Ranking basato sul sistema ELO</p>
            </div>

            <div class="classifica-table-container">
                <table class="classifica-table">
                    <thead>
                        <tr>
                            <th class="position-col">Pos.</th>
                            <th class="avatar-col">Avatar</th>
                            <th class="player-col">Giocatore</th>
                            <th class="level-col">Livello</th>
                            <th class="rating-col">Rating</th>
                        </tr>
                    </thead>
                    <tbody id="classifica-body">
                        <!-- Righe della classifica generate dinamicamente da JavaScript -->
                    </tbody>
                </table>
            </div>

            <div class="classifica-legend">
                <h3>Livelli di abilità</h3>
                <div class="level-description">
                    <p>In base al rating, i giocatori sono classificati in diverse categorie:</p>
                    <ul>
                        <li><span class="level-badge principiante">P</span> Principiante (1000-1199)</li>
                        <li><span class="level-badge dilettante-d">D</span> Dilettante Categoria D (1200-1399)</li>
                        <li><span class="level-badge dilettante-c">C</span> Dilettante Categoria C (1400-1599)</li>
                        <li><span class="level-badge dilettante-b">B</span> Dilettante Categoria B (1600-1799)</li>
                        <li><span class="level-badge dilettante-a">A</span> Dilettante Categoria A (1800-1999)</li>
                        <li><span class="level-badge candidato">CM</span> Candidato Maestro (2000-2199)</li>
                        <li><span class="level-badge maestro">M</span> Maestro (2200-2399)</li>
                        <li><span class="level-badge internazionale">MI</span> Maestro Internazionale (2400-2499)</li>
                        <li><span class="level-badge gran-maestro">GM</span> Gran Maestro (2500-2699)</li>
                        <li><span class="level-badge super">SGM</span> Super Gran Maestro (2700+)</li>
                    </ul>
                </div>
            </div>

            <div class="classifica-buttons">
                <button id="back-to-menu-classifica" class="secondary-btn">
                    <i class="fas fa-arrow-left"></i> Torna al Menu
                </button>
                <button id="refresh-classifica" class="primary-btn">
                    <i class="fas fa-sync"></i> Aggiorna
                </button>
            </div>
        </div>
    </div>

    <!-- Mazzo di carte posizionato al centro assoluto dello schermo -->
    <div class="deck-area">
        <!-- Etichetta descrittiva sopra il mazzo (verrà aggiornata con il nome del giocatore 2) -->
        <div class="deck-label" id="deck-label">Mazzo di gioco</div>
        <div id="deck">
            <div class="card-stack-visual static-deck">
                <div class="card-pile-1"></div>
                <div class="card-pile-2"></div>
                <div class="card-pile-3"></div>
                <div class="card-pile-4"></div>
                <div class="card-pile-5"></div>
            </div>
        </div>
    </div>

    <div id="game-container" style="display: none;">
        <!-- Contenuto del game container rimane lo stesso della home page originale -->
        <!-- Menu laterale compresso per l'interfaccia di gioco -->
        <div id="game-sidebar" class="game-sidebar">
            <div class="logo">
                <img src="img/carte/skemino.webp" alt="Skèmino" class="logo-image">
            </div>
            <nav>
                <ul>
                    <li class="active"><i class="fas fa-play"></i><span>Gioca</span><div class="menu-tooltip">Gioca</div></li>
                    <li><i class="fas fa-book"></i><span>Regole</span><div class="menu-tooltip">Regole</div></li>
                    <li><i class="fas fa-trophy"></i><span>Tornei</span><div class="menu-tooltip">Tornei</div></li>
                    <li><i class="fas fa-graduation-cap"></i><span>Tutorial</span><div class="menu-tooltip">Tutorial</div></li>
                    <li><i class="fas fa-chart-line"></i><span>Statistiche</span><div class="menu-tooltip">Statistiche</div></li>
                    <li><i class="fas fa-users"></i><span>Community</span><div class="menu-tooltip">Community</div></li>
                    <li id="game-classifica-link"><i class="fas fa-medal"></i><span>Classifica</span><div class="menu-tooltip">Classifica</div></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <ul class="sidebar-icons">
                    <li id="game-user-profile">
                        <i class="fas fa-user"></i>
                        <div class="menu-tooltip" id="user-tooltip-text">Utente</div>
                    </li>
                    <li>
                        <i class="fas fa-globe"></i>
                        <div class="menu-tooltip">Italiano</div>
                    </li>
                    <li>
                        <i class="fas fa-question-circle"></i>
                        <div class="menu-tooltip">Supporto</div>
                    </li>
                </ul>
            </div>
        </div>

        <div id="players-column">
            <div id="player1-area">
                <div class="player-header">
                    <h2>
                        <div class="player-dot white"></div>
                        <div class="player-info-box">
                            <span class="player-name">Giocatore 1</span> <span class="player-rating" id="player1-rating"></span>
                            <div class="player-avatar-wrapper">
                                <div class="player-avatar-container" id="player1-avatar-container"></div>
                                <div class="player-avatar-level" id="player1-avatar-level"></div>
                            </div>
                        </div>
                    </h2>
                    <div id="player1-total-timer" class="total-timer-integrated">
                        <div class="hourglass">
                            <div class="hourglass-top"></div>
                            <div class="hourglass-bottom"></div>
                        </div>
                        <div class="timer-wrapper">
                            <i class="fas fa-clock"></i>
                            <span class="total-timer-count">00:00</span>
                        </div>
                    </div>
                </div>

                <div id="player1-hand" class="hand-area">
                    <!-- Carte del giocatore 1 (bianco) verranno aggiunte qui -->
                </div>
            </div>

            <div id="player2-area">
                <div class="player-header">
                    <h2>
                        <div class="player-dot black"></div>
                        <div class="player-info-box">
                            <span class="player-name">Giocatore 2</span> <span class="player-rating" id="player2-rating"></span>
                            <div class="player-avatar-wrapper">
                                <div class="player-avatar-container" id="player2-avatar-container"></div>
                                <div class="player-avatar-level" id="player2-avatar-level"></div>
                            </div>
                        </div>
                    </h2>
                    <div id="player2-total-timer" class="total-timer-integrated">
                        <div class="hourglass">
                            <div class="hourglass-top"></div>
                            <div class="hourglass-bottom"></div>
                        </div>
                        <div class="timer-wrapper">
                            <i class="fas fa-clock"></i>
                            <span class="total-timer-count">00:00</span>
                        </div>
                    </div>
                </div>

                <div id="player2-hand" class="hand-area">
                    <!-- Carte del giocatore 2 (nero) verranno aggiunte qui -->
                </div>
            </div>
        </div>

        <div id="board-area">
            <div id="advantage-indicator">
                <div class="advantage-white">
                    <span class="advantage-label">G1</span>
                </div>
                <div class="advantage-black">
                    <span class="advantage-label">G2</span>
                </div>
            </div>
            <div id="game-board">
                <!-- La griglia di gioco verrà generata qui -->
            </div>

            <div id="board-sidebar">
                <!-- Tab container -->
                <div id="sidebar-tabs" class="sidebar-tabs">
                    <div class="tab active" data-tab="gioca">
                        <i class="fas fa-gamepad"></i>
                        <span class="tab-text">Gioca</span>
                    </div>
                    <div class="tab" data-tab="nuova-partita">
                        <i class="fas fa-play"></i>
                        <span class="tab-text">Nuova Partita</span>
                    </div>
                    <div class="tab" data-tab="analisi">
                        <i class="fas fa-chart-bar"></i>
                        <span class="tab-text">Analisi</span>
                    </div>
                    <div class="tab" data-tab="giocatori">
                        <i class="fas fa-users"></i>
                        <span class="tab-text">Giocatori</span>
                    </div>
                </div>

                <!-- Tab content containers -->
                <div id="sidebar-content">
                    <!-- Gioca tab content -->
                    <div id="tab-gioca" class="tab-content active">
                        <div id="game-message-container">
                            <p id="game-message"></p>
                        </div>

                        <div class="game-actions">
                            <h4><i class="fas fa-chess"></i> Azioni di Gioco</h4>
                            <div class="action-buttons">
                                <button id="draw-card-button" class="action-button">
                                    <i class="fas fa-hand-paper"></i>
                                    <span>Pesca Carta</span>
                                    <span id="deck-counter" class="deck-counter">39</span>
                                </button>
                                <button id="hint-button" class="action-button">
                                    <i class="fas fa-lightbulb"></i>
                                    <span>Suggerimento</span>
                                </button>
                            </div>
                        </div>

                        <div class="game-status">
                            <h4><i class="fas fa-info-circle"></i> Stato Partita</h4>
                            <div class="status-info">
                                <div class="status-row">
                                    <span class="status-label">Turno:</span>
                                    <span class="status-value" id="current-turn">-</span>
                                </div>

                                <div class="status-row move-navigation">
                                    <span class="status-label">Mosse:</span>
                                    <div class="move-navigation-controls">
                                        <button id="prev-move-btn" class="move-nav-btn" title="Mossa precedente">
                                            <i class="fas fa-arrow-left"></i>
                                        </button>
                                        <span class="move-counter" id="current-move-counter">0/0</span>
                                        <button id="next-move-btn" class="move-nav-btn" title="Mossa successiva">
                                            <i class="fas fa-arrow-right"></i>
                                        </button>
                                        <button id="current-move-btn" class="move-nav-btn" title="Torna alla mossa corrente">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Opzioni di fine partita integrate nello stato partita -->
                                <div class="status-row end-game-options-row">
                                    <span class="status-label">Opzioni:</span>
                                    <div class="end-game-buttons-integrated">
                                        <button id="offer-draw-button" class="end-game-button draw-button">
                                            <i class="fas fa-handshake"></i>
                                            <span>Chiedi Patta</span>
                                        </button>
                                        <button id="resign-button" class="end-game-button resign-button">
                                            <i class="fas fa-times-circle"></i>
                                            <span>Abbandona</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sezione Skèmino notation -->
                        <div id="skemino-notation-area">
                            <!-- PSN Visualizer will be initialized here -->
                        </div>

                        <!-- Chat minimalista per il multiplayer -->
                        <div id="chat-area" class="chat-area">
                            <div class="chat-header">
                                <i class="fas fa-comments"></i> Chat Multiplayer
                            </div>
                            <div id="chat-messages" class="chat-messages"></div>
                            <div class="chat-input-container">
                                <input type="text" id="chat-input" placeholder="Invia messaggio..." maxlength="100">
                                <button id="emoji-button" class="emoji-button">
                                    <i class="fas fa-smile"></i>
                                </button>
                            </div>
                            <div id="emoji-picker" class="emoji-picker">
                                <div class="emoji-list">
                                    <span class="emoji-item">😀</span>
                                    <span class="emoji-item">😎</span>
                                    <span class="emoji-item">👍</span>
                                    <span class="emoji-item">👏</span>
                                    <span class="emoji-item">🎮</span>
                                    <span class="emoji-item">🤔</span>
                                    <span class="emoji-item">🙂</span>
                                    <span class="emoji-item">😊</span>
                                    <span class="emoji-item">👋</span>
                                    <span class="emoji-item">🏆</span>
                                    <span class="emoji-item">⭐</span>
                                    <span class="emoji-item">❤️</span>
                                </div>
                            </div>
                        </div>

                        <!-- Modali di conferma (nascosti di default) -->
                        <div id="draw-confirmation-modal" class="game-modal">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4><i class="fas fa-handshake"></i> Conferma Patta</h4>
                                </div>
                                <div class="modal-body">
                                    <p>Sei sicuro di voler proporre una patta all'avversario?</p>
                                    <p class="modal-note">La partita terminerà in parità se l'avversario accetta.</p>
                                </div>
                                <div class="modal-footer">
                                    <button id="confirm-draw" class="confirm-button">
                                        <i class="fas fa-check"></i> Conferma
                                    </button>
                                    <button id="cancel-draw" class="cancel-button">
                                        <i class="fas fa-times"></i> Annulla
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div id="resign-confirmation-modal" class="game-modal">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4><i class="fas fa-times-circle"></i> Conferma Abbandono</h4>
                                </div>
                                <div class="modal-body">
                                    <p>Sei sicuro di voler abbandonare la partita?</p>
                                    <p class="modal-note">Questa azione non può essere annullata e comporterà la sconfitta.</p>
                                </div>
                                <div class="modal-footer">
                                    <button id="confirm-resign" class="confirm-button">
                                        <i class="fas fa-check"></i> Conferma
                                    </button>
                                    <button id="cancel-resign" class="cancel-button">
                                        <i class="fas fa-times"></i> Annulla
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Altri tab content mantengono la struttura originale -->
                    <!-- Nuova Partita tab content -->
                    <div id="tab-nuova-partita" class="tab-content">
                        <!-- Contenuto invariato -->
                    </div>

                    <!-- Analisi tab content -->
                    <div id="tab-analisi" class="tab-content">
                        <!-- Contenuto invariato -->
                    </div>

                    <!-- Giocatori tab content -->
                    <div id="tab-giocatori" class="tab-content">
                        <!-- Contenuto invariato -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script necessari per il funzionamento della pagina -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="auth.js"></script>
    <script src="script.js"></script>
    <script src="js/multiplayer.js"></script>
    <script src="js/psn-unified.js"></script>
    <script src="js/page-transitions.js"></script>

    <!-- Script specifico per la dashboard -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Verifica se l'utente è loggato
            if (!window.authUtils.isLoggedIn()) {
                // Reindirizza alla pagina di login se l'utente non è loggato
                window.location.href = '/login?returnTo=dashboard';
                return;
            }

            // Ottieni i dati dell'utente
            const user = window.authUtils.getCurrentUser();
            if (!user) {
                console.error('Errore nel recupero dei dati utente');
                return;
            }

            // Aggiorna i dati del giocatore nella pagina
            document.getElementById('player-name').textContent = user.username || 'Giocatore';
            document.getElementById('username-display').textContent = user.username || 'Utente';

            // Imposta il livello utente in base al rating
            const rating = user.rating || 1000;
            let level = 'Principiante';
            let levelClass = 'principiante';

            if (rating >= 2700) {
                level = 'Super Gran Maestro';
                levelClass = 'super';
            } else if (rating >= 2500) {
                level = 'Gran Maestro';
                levelClass = 'gran-maestro';
            } else if (rating >= 2400) {
                level = 'Maestro Internazionale';
                levelClass = 'internazionale';
            } else if (rating >= 2200) {
                level = 'Maestro';
                levelClass = 'maestro';
            } else if (rating >= 2000) {
                level = 'Candidato Maestro';
                levelClass = 'candidato';
            } else if (rating >= 1800) {
                level = 'Dilettante A';
                levelClass = 'dilettante-a';
            } else if (rating >= 1600) {
                level = 'Dilettante B';
                levelClass = 'dilettante-b';
            } else if (rating >= 1400) {
                level = 'Dilettante C';
                levelClass = 'dilettante-c';
            } else if (rating >= 1200) {
                level = 'Dilettante D';
                levelClass = 'dilettante-d';
            }

            document.getElementById('user-rank').textContent = level;
            document.getElementById('player-level-badge').textContent = level;
            document.getElementById('player-level-badge').className = 'skill-badge ' + levelClass;

            // Aggiorna le statistiche
            document.getElementById('stats-rating').textContent = rating;

            // Carica le statistiche dal server
            fetchUserStats();

            // Carica le partite recenti dal server
            fetchRecentGames();

            // Carica l'avatar dell'utente
            loadUserAvatar();

            // Gestione bottone di logout
            document.getElementById('logout-button').addEventListener('click', function() {
                window.authUtils.logout();
            });

            // Gestione bottoni di gioco
            document.getElementById('play-online-button').addEventListener('click', function() {
                window.location.href = '/?action=play-online';
            });

            document.getElementById('new-game-button').addEventListener('click', function() {
                window.location.href = '/?action=new-game';
            });

            document.getElementById('training-button').addEventListener('click', function() {
                window.location.href = '/?action=training';
            });

            document.getElementById('start-local-button').addEventListener('click', function() {
                window.location.href = '/?action=local-game';
            });
        });

        // Funzione per caricare le statistiche utente dal server
        function fetchUserStats() {
            // Qui ci sarebbe una chiamata al backend per ottenere le statistiche
            // Per ora uso dati di esempio
            const stats = {
                wins: 12,
                losses: 5,
                draws: 3,
                games: 20,
                winrate: 60
            };

            document.getElementById('stats-wins').textContent = stats.wins;
            document.getElementById('stats-losses').textContent = stats.losses;
            document.getElementById('stats-draws').textContent = stats.draws;
            document.getElementById('stats-games').textContent = stats.games;
            document.getElementById('stats-winrate').textContent = stats.winrate + '%';
        }

        // Funzione per caricare le partite recenti dal server
        function fetchRecentGames() {
            // Qui ci sarebbe una chiamata al backend per ottenere le partite recenti
            // Per ora uso dati di esempio

            const recentGames = [
                {
                    opponent: 'GrandeMaestro99',
                    date: '15 maggio 2023, 18:45',
                    result: 'win',
                    gameId: '12345'
                },
                {
                    opponent: 'StrategaSupremo',
                    date: '14 maggio 2023, 20:12',
                    result: 'loss',
                    gameId: '12344'
                },
                {
                    opponent: 'NovelloSkemino',
                    date: '12 maggio 2023, 16:30',
                    result: 'win',
                    gameId: '12343'
                },
                {
                    opponent: 'MaestroDelTavolo',
                    date: '10 maggio 2023, 19:05',
                    result: 'draw',
                    gameId: '12342'
                }
            ];

            // Mostra le partite nella lista
            const gamesList = document.getElementById('recent-games-list');

            // Se abbiamo partite, rimuovi il messaggio "nessuna partita"
            if (recentGames.length > 0) {
                gamesList.innerHTML = '';

                recentGames.forEach(game => {
                    const gameItem = document.createElement('div');
                    gameItem.className = 'game-item';

                    let resultText = '';
                    let resultClass = '';

                    switch(game.result) {
                        case 'win':
                            resultText = 'Vittoria';
                            resultClass = 'win';
                            break;
                        case 'loss':
                            resultText = 'Sconfitta';
                            resultClass = 'loss';
                            break;
                        case 'draw':
                            resultText = 'Patta';
                            resultClass = 'draw';
                            break;
                    }

                    gameItem.innerHTML = `
                        <div class="game-result ${resultClass}">${resultText}</div>
                        <div class="game-info">
                            <div class="game-opponent">vs. ${game.opponent}</div>
                            <div class="game-date">${game.date}</div>
                        </div>
                        <div class="game-actions">
                            <button class="analyze-game" data-game-id="${game.gameId}">
                                <i class="fas fa-search"></i> Analizza
                            </button>
                        </div>
                    `;

                    gamesList.appendChild(gameItem);
                });

                // Aggiungi listener per i pulsanti di analisi
                document.querySelectorAll('.analyze-game').forEach(button => {
                    button.addEventListener('click', function() {
                        const gameId = this.getAttribute('data-game-id');
                        window.location.href = `/analyze?game=${gameId}`;
                    });
                });
            }
        }

        // Funzione per caricare l'avatar dell'utente
        function loadUserAvatar() {
            const user = window.authUtils.getCurrentUser();

            if (!user) return;

            // Calcola il livello in base al rating
            const rating = user.rating || 1000;
            let avatarLevel = '';

            if (rating >= 2700) {
                avatarLevel = 'Super Gran Maestro';
            } else if (rating >= 2500) {
                avatarLevel = 'Gran Maestro';
            } else if (rating >= 2400) {
                avatarLevel = 'Maestro Internazionale';
            } else if (rating >= 2200) {
                avatarLevel = 'Maestro';
            } else if (rating >= 2000) {
                avatarLevel = 'Candidato Maestro';
            } else if (rating >= 1800) {
                avatarLevel = 'Dilettante A';
            } else if (rating >= 1600) {
                avatarLevel = 'Dilettante B';
            } else if (rating >= 1400) {
                avatarLevel = 'Dilettante C';
            } else if (rating >= 1200) {
                avatarLevel = 'Dilettante D';
            } else {
                avatarLevel = 'Principiante';
            }

            // Crea l'immagine dell'avatar
            const avatarContainer = document.getElementById('player-avatar-container');
            if (avatarContainer) {
                avatarContainer.innerHTML = '';

                const avatar = document.createElement('img');
                avatar.src = `img/avatar/${avatarLevel.replace(' ', '%20')}.webp`;
                avatar.alt = `Avatar ${avatarLevel}`;
                avatar.className = 'player-avatar';
                avatarContainer.appendChild(avatar);
            }
        }
    </script>
</body>
</html>