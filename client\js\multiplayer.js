/**
 * Multiplayer functionality for Skèmino
 * Handles matchmaking, game state synchronization, and turn-based gameplay
 * 
 * DETTAGLI DEL FUNZIONAMENTO:
 * 
 * 1. INIZIALIZZAZIONE:
 *    - Quando game.html viene caricata, initMultiplayer() viene chiamata
 *    - Recupera eventuali dati di match salvati in sessionStorage (da home-logged.html)
 *    - Se trova dati salvati, chiama handleMatchFound() per processarli
 *    - Inizializza tutti i listener per eventi Socket.io
 * 
 * 2. FLUSSO DI MATCHMAKING:
 *    - Da home-logged.html: click su "Nuova Partita" → startMatchmaking()
 *    - Invia richiesta 'findMatch' al server
 *    - Server risponde con 'matchFound' → handleMatchFound()
 *    - handleMatchFound() inizializza GameModeManager e mostra animazione dadi
 * 
 * 3. GESTIONE PARTITA:
 *    - Riceve eventi 'gameState' dal server
 *    - Gestisce timer dei turni e timeout
 *    - Sincronizza le mosse tra i giocatori
 *    - Gestisce chat in-game
 *    - Monitora disconnessioni e riconnessioni
 * 
 * 4. INTEGRAZIONE CON ALTRI MODULI:
 *    - Usa GameModeManager per coordinare la modalità online
 *    - Chiama showGameSetup() da script.js per animazioni
 *    - Si interfaccia con online-game.js per logica specifica online
 */


// Esponiamo showVictoryScreen globalmente per poterla richiamare dai timer
if (typeof window.showVictoryScreen !== 'function') {
    window.showVictoryScreen = function(winner, reason) {
        console.log('Mostrando il riquadro di vittoria...');
        const victoryScreen = document.getElementById('victory-screen');

        if (!victoryScreen) {
            console.error('Elemento victory-screen non trovato!');
            return;
        }

        // Imposta il nome del vincitore e il motivo della vittoria
        const winnerNameElement = document.getElementById('winner-name');
        const victoryReasonElement = document.getElementById('victory-reason');

        if (winnerNameElement) {
            winnerNameElement.textContent = winner || 'Giocatore';
        }

        if (victoryReasonElement) {
            victoryReasonElement.textContent = reason || 'Hai vinto la partita!';
        }

        // Assicurati che il riquadro di vittoria sia visibile
        victoryScreen.style.display = 'flex';

        // Riproduci suono di vittoria
        if (window.gameAudio && window.gameAudio.play) {
            window.gameAudio.play('victory', 0.7);
        }

        console.log('Riquadro di vittoria mostrato.');
    };
}

// Multiplayer state
const multiplayer = {
    isMatchmaking: false,
    currentGameId: null,
    inviteCode: null,
    opponent: null,
    turnTimer: null,
    turnTimeRemaining: 0,
    turnTimeout: 60, // Default timeout in seconds
    notifications: [],
    matchmakingTimeout: null // Timer per il messaggio quando non trova avversari
};

// DOM Elements
let matchmakingModal;
let matchmakingStatus;
let matchmakingCancel;
let createGameBtn;
let joinGameBtn;
let inviteCodeInput;
let turnTimerElement;
let notificationContainer;

// Initialize multiplayer functionality
function initMultiplayer() {
    // Initialize DOM elements
    matchmakingModal = document.getElementById('matchmaking-modal');
    matchmakingStatus = document.getElementById('matchmaking-status');
    matchmakingCancel = document.getElementById('cancel-matchmaking');
    createGameBtn = document.getElementById('create-game-btn');
    joinGameBtn = document.getElementById('join-game-btn');
    inviteCodeInput = document.getElementById('invite-code-input');
    turnTimerElement = document.getElementById('turn-timer');
    notificationContainer = document.getElementById('notification-container');
    
    // Controlla se abbiamo dei dati salvati in sessionStorage
    const savedGameData = sessionStorage.getItem('gameData');
    if (savedGameData) {
        console.log('[MULTIPLAYER] Dati di gioco trovati in sessionStorage:', savedGameData);
        const gameData = JSON.parse(savedGameData);
        sessionStorage.removeItem('gameData'); // Rimuovi i dati dopo l'uso
        
        // Processa direttamente i dati del match
        setTimeout(() => {
            handleMatchFound(gameData);
        }, 500); // Piccolo ritardo per permettere l'inizializzazione completa
    }

    // Add event listeners
    if (matchmakingCancel) {
        // Rimuoviamo eventuali listener esistenti
        matchmakingCancel.removeEventListener('click', cancelMatchmaking);
        // Aggiungiamo un nuovo listener con una funzione più robusta
        matchmakingCancel.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Cancel button clicked directly');
            cancelMatchmaking();
        });
    }

    if (createGameBtn) {
        createGameBtn.addEventListener('click', createPrivateGame);
    }

    if (joinGameBtn) {
        joinGameBtn.addEventListener('click', joinPrivateGame);
    }

    // Initialize Socket.io event handlers
    initSocketEvents();

    // console.log('Multiplayer functionality initialized');
}

// Initialize Socket.io event handlers
function initSocketEvents() {
    // Get socket from window.socket
    const socket = window.socket;
    
    if (!socket) {
        console.error('[MULTIPLAYER] Socket non disponibile, riprovando tra 500ms');
        // Retry after a short delay
        setTimeout(initSocketEvents, 500);
        return;
    }
    
    // Matchmaking events
    socket.on('waitingForOpponent', handleWaitingForOpponent);
    
    // Disabilita matchFound handler sulla home page
    if (!window.location.pathname.includes('home') && !window.location.pathname.includes('index')) {
        socket.on('matchFound', handleMatchFound);
    } else {
        console.log('[MULTIPLAYER] Handler matchFound disabilitato su home page');
    }
    
    socket.on('matchmakingCancelled', handleMatchmakingCancelled);

    // Game events
    socket.on('gameCreated', handleGameCreated);
    socket.on('playerJoined', handlePlayerJoined);
    socket.on('gameOver', handleGameOver);
    // Nota: gameState è già gestito in script.js, quindi non lo duplichiamo qui
    
    // Collegamento all'evento gameState per garantire che gli slot delle mani siano sempre creati
    socket.on('gameState:handSlots', function() {
        // Ricreiamo gli slot delle mani dopo ogni aggiornamento di stato
        setTimeout(createHandSlots, 100);
    });

    // Reconnection events
    socket.on('playerDisconnected', handlePlayerDisconnected);
    socket.on('playerReconnected', handlePlayerReconnected);
    socket.on('recoverySuccess', handleRecoverySuccess);
    socket.on('disconnectionWarning', handleDisconnectionWarning);
    socket.on('reconnectionSuccessful', handleReconnectionSuccessful);

    // Notification events
    socket.on('notification', handleNotification);

    // Chat events
    socket.on('chatMessage', handleChatMessage);

    // console.log('[SOCKET] Inizializzati event handler per socket.io');
}

// Questa funzione non serve più in quanto gameState è gestito in script.js
// Manteniamo solo la funzione di aggiornamento dei timer che è specifica del multiplayer
function updateGameStateForMultiplayer(gameState) {
    // Aggiorna visivamente quale giocatore è attivo
    const isPlayer1Turn = gameState.players && gameState.currentPlayerId &&
                        gameState.players[gameState.currentPlayerId]?.color === 'white';
    const isPlayer2Turn = gameState.players && gameState.currentPlayerId &&
                        gameState.players[gameState.currentPlayerId]?.color === 'black';

    // Aggiorna le classi active sui timer totali
    const player1TimerEl = document.getElementById('player1-total-timer');
    const player2TimerEl = document.getElementById('player2-total-timer');

    if (player1TimerEl) {
        if (isPlayer1Turn) player1TimerEl.classList.add('active');
        else player1TimerEl.classList.remove('active');
    }

    if (player2TimerEl) {
        if (isPlayer2Turn) player2TimerEl.classList.add('active');
        else player2TimerEl.classList.remove('active');
    }
}

// Start matchmaking
function startMatchmaking() {
    // Aggiungi log di debug per verificare lo stato dell'autenticazione
    console.log('[MULTIPLAYER AUTH DEBUG] Verifica autenticazione:');
    console.log('- Token presente:', !!localStorage.getItem('token'));
    console.log('- User presente:', !!localStorage.getItem('user'));
    console.log('- authUtils.isLoggedIn():', window.authUtils && window.authUtils.isLoggedIn());

    if (!isUserLoggedIn()) {
        showLoginPrompt('Per giocare online devi effettuare l\'accesso');
        return;
    }

    // Verifica che il socket sia connesso
    if (!socket || !socket.connected) {
        console.log('[MULTIPLAYER AUTH DEBUG] Socket non connesso, tentativo di reinizializzazione');
        // Reinizializza il socket
        if (window.reinitializeSocket && typeof window.reinitializeSocket === 'function') {
            window.reinitializeSocket();
            // Attendi che il socket si connetta
            setTimeout(() => {
                if (socket && socket.connected) {
                    console.log('[MULTIPLAYER AUTH DEBUG] Socket riconnesso con successo');
                    // Riprova il matchmaking
                    startMatchmaking();
                } else {
                    console.log('[MULTIPLAYER AUTH DEBUG] Impossibile riconnettere il socket');
                    showError('Errore di connessione al server. Ricarica la pagina e riprova.');
                }
            }, 1000);
            return;
        } else {
            showError('Errore di connessione al server. Ricarica la pagina e riprova.');
            return;
        }
    }

    // Log di debug per confermare l'autenticazione
    console.log('[MULTIPLAYER AUTH DEBUG] Utente autenticato con successo');
    console.log('- User:', localStorage.getItem('user'));

    multiplayer.isMatchmaking = true;

    // Show matchmaking modal
    if (matchmakingModal) {
        matchmakingModal.style.display = 'flex';
        matchmakingStatus.textContent = 'Ricerca di un avversario in corso...';
    }

    // Get user rating
    const userRating = getUserRating();

    // Log socket authentication status
    console.log('[MATCHMAKING DEBUG] Socket authentication status:');
    console.log('- Socket ID:', socket.id);
    console.log('- Socket connected:', socket.connected);
    console.log('- Auth token present:', !!localStorage.getItem('token'));
    console.log('- Socket auth:', socket.auth);

    // Verifica che il socket abbia l'autenticazione
    if (!socket.auth || !socket.auth.token) {
        console.log('[MATCHMAKING DEBUG] Socket senza token di autenticazione, tentativo di aggiornamento');
        socket.auth = { token: localStorage.getItem('token') };
    }

    // Mostra il modale di matchmaking
    if (matchmakingModal) {
        matchmakingModal.style.display = 'flex';
        if (matchmakingStatus) {
            matchmakingStatus.textContent = 'Ricerca avversario in corso...';
        }
    }

    // Send matchmaking request to server
    socket.emit('findMatch', {
        rating: userRating,
        username: window.authUtils?.getCurrentUser()?.username || 'Giocatore'
    });

    console.log('Matchmaking started');

    // Imposta un timer di 1 minuto per mostrare messaggio se non trova avversari
    const matchmakingTimeout = setTimeout(() => {
        if (multiplayer.isMatchmaking) {
            // Se dopo 1 minuto è ancora in ricerca, mostra messaggio
            if (matchmakingStatus) {
                matchmakingStatus.textContent = 'Non abbiamo trovato avversari disponibili. Puoi continuare ad attendere o riprovare più tardi.';
            }

            // Mostra anche una notifica
            showNotification('Non ci sono avversari disponibili al momento. Riprova più tardi o continua ad attendere.', 8000);
        }
    }, 60000); // 60 secondi = 1 minuto

    // Memorizza il timeout per poterlo cancellare se necessario
    multiplayer.matchmakingTimeout = matchmakingTimeout;

    // Gestione errori di autenticazione
    socket.once('gameError', (errorMessage) => {
        console.log('[MATCHMAKING ERROR]', errorMessage);

        // Cancella il timeout se esiste
        if (multiplayer.matchmakingTimeout) {
            clearTimeout(multiplayer.matchmakingTimeout);
            multiplayer.matchmakingTimeout = null;
        }

        // Nascondi il modale di matchmaking
        if (matchmakingModal) {
            matchmakingModal.style.display = 'none';
        }

        // Se l'errore è relativo all'autenticazione, mostra il prompt di login
        if (errorMessage.includes('autenticato') || errorMessage.includes('login')) {
            // Rimuovi il token e l'utente dal localStorage
            localStorage.removeItem('token');
            localStorage.removeItem('user');

            // Mostra il prompt di login
            showLoginPrompt('Sessione scaduta. Effettua nuovamente l\'accesso per giocare online.');
        } else {
            // Mostra l'errore generico
            showError(errorMessage);
        }

        if (errorMessage.includes('autenticato') || errorMessage.includes('login')) {
            console.log('[MATCHMAKING AUTH ERROR] Problema di autenticazione rilevato, tentativo di risoluzione...');

            // Nascondi il modal di matchmaking
            if (matchmakingModal) {
                matchmakingModal.style.display = 'none';
            }

            // Mostra un messaggio di errore
            showError('Problema di autenticazione rilevato. Ricarica la pagina e riprova.');

            // Suggerisci di effettuare nuovamente il login
            if (confirm('Problema di autenticazione rilevato. Vuoi effettuare nuovamente il login?')) {
                // Pulisci i dati di autenticazione
                localStorage.removeItem('token');
                localStorage.removeItem('user');

                // Reindirizza alla pagina di login
                window.location.href = '/login?returnTo=game';
            }
        }
    });
}

// Cancel matchmaking
function cancelMatchmaking() {
    console.log('Cancel matchmaking button clicked');

    if (!multiplayer.isMatchmaking) {
        console.log('Not in matchmaking state, ignoring cancel request');
        return;
    }

    // Send cancel request to server
    socket.emit('cancelMatchmaking');

    // Forziamo l'annullamento del matchmaking anche sul client
    handleMatchmakingCancelled();

    console.log('Matchmaking cancelled');
}

// Create a private game
function createPrivateGame() {
    if (!isUserLoggedIn()) {
        showLoginPrompt('Per creare una partita privata devi effettuare l\'accesso');
        return;
    }

    // Get turn timeout from input
    const turnTimeoutInput = document.getElementById('turn-timeout-input');
    const turnTimeout = turnTimeoutInput ? parseInt(turnTimeoutInput.value) || 60 : 60;

    // Send create game request to server
    socket.emit('createPrivateGame', {
        turnTimeout,
        rating: getUserRating()
    });

    // Show loading state
    if (createGameBtn) {
        createGameBtn.disabled = true;
        createGameBtn.textContent = 'Creazione partita...';
    }

    console.log('Creating private game');
}

// Join a private game
function joinPrivateGame() {
    if (!isUserLoggedIn()) {
        showLoginPrompt('Per unirti a una partita privata devi effettuare l\'accesso');
        return;
    }

    // Get invite code from input
    const inviteCode = inviteCodeInput ? inviteCodeInput.value.trim() : '';

    if (!inviteCode) {
        showError('Inserisci un codice di invito valido');
        return;
    }

    // Send join game request to server
    socket.emit('joinPrivateGame', {
        inviteCode,
        rating: getUserRating()
    });

    // Show loading state
    if (joinGameBtn) {
        joinGameBtn.disabled = true;
        joinGameBtn.textContent = 'Accesso in corso...';
    }

    console.log('Joining private game with code:', inviteCode);
}

// Handle waiting for opponent
function handleWaitingForOpponent() {
    if (matchmakingStatus) {
        matchmakingStatus.textContent = 'In attesa di un avversario...';
    }

    console.log('Waiting for opponent');
}

// Handle match found
function handleMatchFound(data) {
    console.log('[MATCH FOUND] Ricevuto evento matchFound:', data);

    multiplayer.isMatchmaking = false;
    multiplayer.currentGameId = data.gameId;
    multiplayer.opponent = data.opponent;

    // Cancella il timeout di ricerca se esiste
    if (multiplayer.matchmakingTimeout) {
        clearTimeout(multiplayer.matchmakingTimeout);
        multiplayer.matchmakingTimeout = null;
    }

    // Hide matchmaking modal
    if (matchmakingModal) {
        matchmakingModal.style.display = 'none';
    }
    
    // APPLICA IMMEDIATAMENTE gli stili online per evitare compressione durante l'animazione
    if (window.applyOnlinePlayInterfaceImmediate) {
        console.log('[MATCH FOUND] Applicando immediatamente stili online-play-interface');
        window.applyOnlinePlayInterfaceImmediate();
    } else {
        console.log('[MATCH FOUND] Funzione applyOnlinePlayInterfaceImmediate non disponibile');
    }
    
    // Crea gli slot per le carte nelle aree delle mani in modalità multiplayer
    createHandSlots();
    
    // IMPORTANTE: Imposta prima il playerId persistente
    // Il server invia l'ID esatto che usa per questo giocatore
    window.myPlayerId = data.playerId;
    // Sincronizza anche la variabile locale myPlayerId se esiste
    if (window.myPlayerId && typeof window.myPlayerId !== 'undefined') {
        try {
            // Assegna direttamente invece di usare eval per evitare errori di inizializzazione
            if (typeof myPlayerId !== 'undefined') {
                myPlayerId = window.myPlayerId;
            }
        } catch (error) {
            console.log('[MATCH FOUND] myPlayerId non ancora inizializzato, verrà impostato successivamente');
        }
    }
    console.log('[MATCH FOUND] myPlayerId impostato PRIMA del salvataggio nomi:', window.myPlayerId);
    console.log('[MATCH FOUND] data.playerId:', data.playerId, 'socket.id:', socket.id);
    console.log('[MATCH FOUND] data.opponentId:', data.opponentId);
    
    // Imposta immediatamente i nomi dei giocatori solo se abbiamo nomi reali
    // CORREZIONE: Salviamo immediatamente i dati del giocatore nella memoria permanente
    // per evitare inversioni dei nomi
    const currentUser = window.authUtils?.getCurrentUser();
    const myName = currentUser?.username;
    const opponentName = data.opponent?.name;
    
    console.log('[MATCH FOUND] Nome utente corrente:', myName);
    console.log('[MATCH FOUND] Nome avversario:', opponentName);
    console.log('[MATCH FOUND] Il mio colore:', data.color);
    
    // Memorizza i dati del match globalmente per uso futuro
    window.matchData = {
        myName: myName,
        opponentName: opponentName,
        myColor: data.color,
        opponentColor: data.color === 'white' ? 'black' : 'white'
    };
    console.log('[MATCH FOUND] Dati match memorizzati:', JSON.stringify(window.matchData));
    
    // Salva immediatamente nella memorizzazione permanente usando gli ID esatti dal server
    // IMPORTANTE: Usa sempre data.playerId e data.opponentId dal server, mai socket.id
    if (data.playerId && myName && myName !== 'Tu') {
        window.permanentPlayerNames[data.playerId] = myName;
        window.permanentPlayerColors[data.playerId] = data.color;
        console.log(`[MATCH FOUND DEBUG] Salvato ME: ID=${data.playerId}, nome=${myName}, colore=${data.color}`);
    }
    if (data.opponentId && opponentName && opponentName !== 'Avversario') {
        const opponentColor = data.color === 'white' ? 'black' : 'white';
        window.permanentPlayerNames[data.opponentId] = opponentName;
        window.permanentPlayerColors[data.opponentId] = opponentColor;
        console.log(`[MATCH FOUND DEBUG] Salvato AVVERSARIO: ID=${data.opponentId}, nome=${opponentName}, colore=${opponentColor}`);
    }
    console.log('[MATCH FOUND] Nomi permanenti salvati:', JSON.stringify(window.permanentPlayerNames));
    console.log('[MATCH FOUND] Colori permanenti salvati:', JSON.stringify(window.permanentPlayerColors));
    
    // NON impostiamo i nomi nel DOM qui, lasciamo che updatePlayerAreaNames
    // lo faccia correttamente quando arriva il gameState
    console.log('[MATCH FOUND] Nomi salvati permanentemente, delegando impostazione DOM a updatePlayerAreaNames');

    // Verifica che il socket sia connesso
    if (!socket || !socket.connected) {
        console.error('[MATCH FOUND ERROR] Socket non connesso durante matchFound');
        showError('Errore di connessione al server. Ricarica la pagina e riprova.');
        return;
    }

    // Assicurati che il gameId sia salvato nel socket
    socket.gameId = data.gameId;

    // Aggiorna il rating dell'avversario nell'interfaccia
    if (typeof window.updatePlayerRatings === 'function') {
        window.updatePlayerRatings();
    } else {
        // Fallback se la funzione non è disponibile
        const player2RatingElement = document.getElementById('player2-rating');
        if (player2RatingElement && data.opponent && data.opponent.rating) {
            player2RatingElement.textContent = `(${data.opponent.rating})`;
        }
    }

    // Aggiorna direttamente l'elemento del rating del secondo giocatore
    updateOpponentRatingDisplay();

    // Mostra la chat e assicurati che sia configurata per il multiplayer
    const chatArea = document.getElementById('chat-area');
    if (chatArea) {
        chatArea.style.display = 'block';
        chatArea.classList.add('multiplayer-active');
        console.log('[CHAT] Chat abilitata per il multiplayer');
    }

    // Aggiungi un messaggio di benvenuto nella chat
    chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        // Pulisci eventuali messaggi precedenti
        chatMessages.innerHTML = '';

        // Aggiungi il messaggio di benvenuto
        const welcomeMessage = document.createElement('div');
        welcomeMessage.className = 'chat-message system';
        welcomeMessage.textContent = `Connesso alla partita con ${data.opponent.name}. Buon divertimento!`;
        chatMessages.appendChild(welcomeMessage);

        // Aggiungi un messaggio di test
        setTimeout(() => {
            console.log('[CHAT] TEST: Inviando messaggio alla chat');
            // Simula l'invio di un messaggio al server
            socket.emit('chatMessage', {
                gameId: multiplayer.currentGameId,
                message: "Test della chat - Ignorare questo messaggio"
            });
        }, 2000);

        // Scroll in basso
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Imposta la modalità online nel game mode manager PRIMA di altre inizializzazioni
    window.isOnlineMode = true;
    console.log('[MATCH FOUND] Modalità online impostata');
    console.log('[MATCH FOUND] myPlayerId già impostato in precedenza:', window.myPlayerId);

    // Inizializza il game mode manager per partite online se presente
    if (window.gameModeManager) {
        // Assicurati che il socket sia inizializzato nel game mode manager
        if (!window.gameModeManager.socket) {
            window.gameModeManager.initialize(socket);
        }
        
        console.log('[MATCH FOUND] Inizializzazione game mode manager per partita online');
        window.gameModeManager.startOnlineGame(window.myPlayerId);
    }
    
    // Usa la funzione showGameSetup dal script.js principale per mostrare correttamente l'animazione
    if (typeof window.showGameSetup === 'function') {
        console.log('[MATCH FOUND] Chiamando showGameSetup per mostrare animazione dadi');
        window.showGameSetup();
    } else {
        // Fallback se showGameSetup non è disponibile
        console.log('[MATCH FOUND] showGameSetup non disponibile, utilizzo fallback');
        const homepage = document.getElementById('homepage');
        const mainMenu = document.getElementById('main-menu');
        const playerNamesScreen = document.getElementById('player-names-screen');
        const setupAnimation = document.getElementById('setup-animation');
        const gameContainer = document.getElementById('game-container');
        
        if (homepage) homepage.style.display = 'none';
        if (mainMenu) mainMenu.style.display = 'none';
        if (playerNamesScreen) playerNamesScreen.style.display = 'none';
        
        if (setupAnimation && gameContainer) {
            setupAnimation.style.display = 'block';
            gameContainer.style.display = 'none';
            gameContainer.classList.remove('ready-for-play');
            
            const diceArea = document.getElementById('dice-area');
            const diceResultText = document.getElementById('dice-result-text');
            if (diceArea) diceArea.innerHTML = '';
            if (diceResultText) diceResultText.textContent = 'Lancio dei dadi...';
            
            window.isGameRunning = false;
            window.isSetupAnimating = true;
        }
    }
    
    // Indica che stiamo per ricevere lo stato iniziale del gioco
    window.expectingInitialGameState = true;
    window.hasReceivedInitialState = false;
    window.diceAnimationShown = false; // Reset anche il flag animazione dadi
    window.diceAnimationCompletedOnce = false; // Reset il flag animazione completata
    
    // Assicuriamoci che il socket sia configurato correttamente
    socket.gameId = data.gameId;
    
    // Verifica che l'utente sia loggato
    const loggedUser = window.authUtils?.getCurrentUser();
    if (!loggedUser) {
        console.error('[MATCH FOUND] Utente non loggato!');
        showError('Errore: utente non autenticato');
        return;
    }
    
    console.log('[MATCH FOUND] Socket configurato:', {
        gameId: socket.gameId,
        myPlayerId: window.myPlayerId,
        userId: loggedUser.id,
        userName: loggedUser.username
    });
    
    // Prima di richiedere lo stato, uniamoci alla room del gioco con più informazioni
    const joinData = {
        gameId: data.gameId,
        userId: loggedUser.id,
        userName: loggedUser.username,
        color: data.color
    };
    console.log('[MATCH FOUND] Entrando nella room del gioco con dati:', joinData);
    socket.emit('joinGame', joinData);
    
    // Aggiungi un breve ritardo prima di richiedere lo stato del gioco
    // per assicurarsi che il server abbia avuto il tempo di inizializzare la partita
    setTimeout(() => {
        // Richiedi lo stato con tutte le informazioni necessarie
        const stateRequest = {
            gameId: data.gameId,
            userId: window.authUtils?.getCurrentUser()?.id,
            color: data.color
        };
        
        console.log('[MATCH FOUND] Richiesta stato del gioco con info complete:', stateRequest);
        socket.emit('requestGameState', stateRequest);
        
        // Secondo tentativo se necessario
        setTimeout(() => {
            if (multiplayer.currentGameId && !window.isGameRunning) {
                console.log('[MATCH FOUND] Secondo tentativo di richiesta stato:', stateRequest);
                socket.emit('requestGameState', stateRequest);
            }
        }, 2000);
    }, 1000);

    // Show match found notification
    // showNotification(`Partita trovata! Avversario: ${data.opponent.name} (${data.opponent.rating})`);

    console.log('[MATCH FOUND] Match trovato e processato:', data);
}

// Handle matchmaking cancelled
function handleMatchmakingCancelled() {
    multiplayer.isMatchmaking = false;

    // Cancella il timeout di ricerca se esiste
    if (multiplayer.matchmakingTimeout) {
        clearTimeout(multiplayer.matchmakingTimeout);
        multiplayer.matchmakingTimeout = null;
    }

    // Hide matchmaking modal
    if (matchmakingModal) {
        matchmakingModal.style.display = 'none';
    }

    // Reset UI
    if (matchmakingStatus) {
        matchmakingStatus.textContent = 'Ricerca annullata';
    }

    console.log('Matchmaking cancelled');
}

// Handle game created
function handleGameCreated(data) {
    multiplayer.currentGameId = data.gameId;
    multiplayer.inviteCode = data.inviteCode;

    // Reset UI
    if (createGameBtn) {
        createGameBtn.disabled = false;
        createGameBtn.textContent = 'Crea Partita Privata';
    }

    // Show invite code
    const inviteCodeDisplay = document.getElementById('invite-code-display');
    if (inviteCodeDisplay) {
        inviteCodeDisplay.textContent = data.inviteCode;
        inviteCodeDisplay.parentElement.style.display = 'block';
    }

    // Show notification
    showNotification(`Partita privata creata! Codice invito: ${data.inviteCode}`);

    console.log('Game created:', data);
}

// Handle player joined
function handlePlayerJoined(data) {
    // Show notification
    showNotification(`${data.playerName} si è unito alla partita!`);

    // Reset UI
    if (joinGameBtn) {
        joinGameBtn.disabled = false;
        joinGameBtn.textContent = 'Unisciti';
    }

    console.log('Player joined:', data);
}

// Handle game over
function handleGameOver(data) {
    // Stop turn timer
    stopTurnTimer();

    // Show rating changes if available
    if (data.ratingUpdates) {
        const { winner, loser } = data.ratingUpdates;

        // Check if current user is winner
        const isWinner = winner.userId === getCurrentUserId();

        // Show appropriate message
        if (isWinner) {
            showNotification(`Hai vinto! Rating: ${winner.oldRating} → ${winner.newRating} (${winner.change > 0 ? '+' : ''}${winner.change})`);
        } else {
            showNotification(`Hai perso. Rating: ${loser.oldRating} → ${loser.newRating} (${loser.change > 0 ? '+' : ''}${loser.change})`);
        }

        // Check for level up
        if (winner.oldLevel && winner.level && winner.oldLevel.name !== winner.level.name) {
            if (isWinner) {
                showLevelUpNotification(winner.oldLevel, winner.level);
            } else {
                // Show opponent level up
                showNotification(`Il tuo avversario è salito di livello: ${winner.oldLevel.name} → ${winner.level.name}!`);
            }
        }

        // Check for level down
        if (loser.oldLevel && loser.level && loser.oldLevel.name !== loser.level.name) {
            if (!isWinner) {
                showLevelDownNotification(loser.oldLevel, loser.level);
            }
        }

        // Salva le informazioni di aggiornamento rating nello stato del gioco per la schermata di vittoria
        if (window.currentGameState) {
            window.currentGameState.ratingUpdates = data.ratingUpdates;
        }
    }

    console.log('Game over:', data);
}

// Handle notification
function handleNotification(data) {
    // Add to notifications list
    multiplayer.notifications.push(data);

    // Show notification
    showNotification(data.message);

    console.log('Notification received:', data);
}

// Start turn timer
function startTurnTimer(seconds) {
    // Check if game is ready for play (animation is complete)
    const gameContainerElement = document.getElementById('game-container');
    const isGameReady = gameContainerElement && gameContainerElement.classList.contains('ready-for-play');

    if (!isGameReady) {
        console.log('[TIMER] Non avvio il timer di turno perché l\'animazione è ancora in corso');
        return; // Don't start the timer if game is not ready
    }

    // Stop existing timer
    stopTurnTimer();

    // Set initial time
    multiplayer.turnTimeRemaining = seconds || multiplayer.turnTimeout;

    // Update timer display
    updateTurnTimerDisplay();

    console.log('[TIMER] Avvio timer di turno: ' + multiplayer.turnTimeRemaining + ' secondi rimanenti');

    // Start timer
    multiplayer.turnTimer = setInterval(() => {
        multiplayer.turnTimeRemaining--;

        // Update timer display
        updateTurnTimerDisplay();

        // Solo ferma il timer se scade
        if (multiplayer.turnTimeRemaining <= 0) {
            stopTurnTimer();
            // La partita continua anche se il tempo di turno è scaduto
        }
    }, 1000);
}

// Stop turn timer
function stopTurnTimer() {
    if (multiplayer.turnTimer) {
        clearInterval(multiplayer.turnTimer);
        multiplayer.turnTimer = null;
    }
}

// Update turn timer display
function updateTurnTimerDisplay() {
    if (!turnTimerElement) return;

    // Format time
    const minutes = Math.floor(multiplayer.turnTimeRemaining / 60);
    const seconds = multiplayer.turnTimeRemaining % 60;
    const formattedTime = `${minutes}:${seconds.toString().padStart(2, '0')}`;

    // Update display
    turnTimerElement.textContent = formattedTime;

    // Add warning class if time is running low
    if (multiplayer.turnTimeRemaining <= 10) {
        turnTimerElement.classList.add('warning');
    } else {
        turnTimerElement.classList.remove('warning');
    }
}

// Show notification
function showNotification(message, duration = 5000, type = 'default') {
    if (!notificationContainer) return;

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    // Check if it's a disconnection notification
    if (message.includes('disconnesso') || message.includes('riconnesso')) {
        // Create enhanced notification with icon
        notification.innerHTML = `
            <div class="notification-icon">
                ${message.includes('disconnesso') ?
                    '<i class="fas fa-wifi" style="color: #ff4d4d;"></i>' :
                    '<i class="fas fa-plug" style="color: #50d080;"></i>'}
            </div>
            <div class="notification-content">
                <strong>${message.includes('disconnesso') ? 'Disconnessione' : 'Riconnessione'}</strong>
                <p>${message}</p>
            </div>
        `;
    } else {
        // Regular notification
        notification.textContent = message;
    }

    // Add to container
    notificationContainer.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Remove after duration
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, duration);

    // Also log to console for debugging
    console.log(`[NOTIFICATION] ${message}`);

    return notification;
}

// Show level up notification
function showLevelUpNotification(oldLevel, newLevel) {
    // Create level up modal
    const levelUpModal = document.createElement('div');
    levelUpModal.className = 'level-up-modal';
    levelUpModal.innerHTML = `
        <div class="level-up-content">
            <h2>Livello Aumentato!</h2>
            <div class="level-change">
                <div class="old-level">
                    <img src="img/ranks/${oldLevel.avatar}" alt="${oldLevel.name}">
                    <p>${oldLevel.name}</p>
                </div>
                <div class="level-arrow">→</div>
                <div class="new-level">
                    <img src="img/ranks/${newLevel.avatar}" alt="${newLevel.name}">
                    <p>${newLevel.name}</p>
                </div>
            </div>
            <button class="close-btn">Continua</button>
        </div>
    `;

    // Add to body
    document.body.appendChild(levelUpModal);

    // Show modal
    setTimeout(() => {
        levelUpModal.classList.add('show');
    }, 10);

    // Add close button event
    const closeBtn = levelUpModal.querySelector('.close-btn');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            levelUpModal.classList.remove('show');
            setTimeout(() => {
                levelUpModal.remove();
            }, 300);
        });
    }

    // Play level up sound
    if (gameAudio && gameAudio.play) {
        gameAudio.play('levelUp', 0.7);
    }
}

// Show level down notification
function showLevelDownNotification(oldLevel, newLevel) {
    // Show notification
    showNotification(`Sei sceso di livello: ${oldLevel.name} → ${newLevel.name}.`, 8000);
}

// Handle player disconnection
function handlePlayerDisconnected(data) {
    console.log('[DISCONNECT] Opponent disconnected:', data);

    // Create a countdown overlay
    const countdownOverlay = document.createElement('div');
    countdownOverlay.id = 'reconnection-overlay';
    countdownOverlay.innerHTML = `
        <div class="reconnection-content">
            <h3>Avversario disconnesso</h3>
            <p>L'avversario ha <span id="reconnection-countdown">${data.reconnectionTimeLeft}</span> secondi per riconnettersi</p>
            <div class="reconnection-progress-bar">
                <div class="reconnection-progress" id="reconnection-progress"></div>
            </div>
            <p>Il tempo continua a scorrere</p>
        </div>
    `;
    document.body.appendChild(countdownOverlay);

    // Update countdown every second
    const maxTime = data.reconnectionTimeLeft;
    let timeLeft = data.reconnectionTimeLeft;
    const countdownElement = document.getElementById('reconnection-countdown');
    const progressElement = document.getElementById('reconnection-progress');

    if (progressElement) {
        progressElement.style.width = '100%';
    }

    // Play disconnect sound
    if (window.gameAudio && window.gameAudio.play) {
        window.gameAudio.play('error', 0.7);
    }

    // Start countdown
    const countdownInterval = setInterval(() => {
        timeLeft--;

        if (countdownElement) {
            countdownElement.textContent = timeLeft;
        }

        if (progressElement) {
            progressElement.style.width = `${(timeLeft / maxTime) * 100}%`;
        }

        // When time is getting low (less than 10 seconds), add warning class
        if (timeLeft <= 10 && countdownElement) {
            countdownElement.classList.add('warning-flash');
            progressElement.classList.add('warning-flash');
        }

        // Final warning at 5 seconds
        if (timeLeft === 5) {
            showNotification(`L'avversario ha solo 5 secondi per riconnettersi!`, 4000, 'warning');

            // Extra warning sound
            if (window.gameAudio && window.gameAudio.play) {
                window.gameAudio.play('error', 0.5);
            }
        }

        if (timeLeft <= 0) {
            clearInterval(countdownInterval);

            // Show victory message due to opponent timeout
            showNotification('Vittoria! L\'avversario non si è riconnesso in tempo.', 8000, 'success');

            // Remove overlay after animation
            if (countdownOverlay) {
                countdownOverlay.classList.add('fade-out');
                setTimeout(() => {
                    countdownOverlay.remove();
                }, 500);
            }
        }
    }, 1000);

    // Store the interval in a global variable for cleanup
    window.reconnectionCountdown = {
        interval: countdownInterval,
        overlayElement: countdownOverlay
    };

    // Show warning message
    showNotification(`Avversario disconnesso. Ha ${data.reconnectionTimeLeft} secondi per riconnettersi.`, 6000, 'error');
}

// Handle player reconnection
function handlePlayerReconnected(data) {
    console.log('[RECONNECT] Opponent reconnected:', data);

    // Clear any existing reconnection countdown
    if (window.reconnectionCountdown) {
        clearInterval(window.reconnectionCountdown.interval);

        // Remove overlay with fade-out animation
        if (window.reconnectionCountdown.overlayElement) {
            window.reconnectionCountdown.overlayElement.classList.add('fade-out');
            setTimeout(() => {
                window.reconnectionCountdown.overlayElement.remove();
            }, 500);
        }

        window.reconnectionCountdown = null;
    }

    // Play reconnect sound
    if (window.gameAudio && window.gameAudio.play) {
        window.gameAudio.play('bonus', 0.7);
    }

    // Show notification solo se non si tratta di una nuova partita
    if (localStorage.getItem('disconnected_game_state')) {
        // Show enhanced notification
        showNotification(`Avversario riconnesso dopo ${data.reconnectionTime} secondi! Il gioco continua.`, 5000, 'success');
    }

    // Request fresh game state just to be sure
    setTimeout(() => {
        if (socket && socket.connected) {
            socket.emit('requestGameState');
        }
    }, 500);
}

// Handle successful reconnection
function handleReconnectionSuccessful(data) {
    console.log('[RECONNECTION] Successful reconnection:', data);
    
    // Update the player ID with the new socket ID
    if (data.newPlayerId) {
        window.myPlayerId = data.newPlayerId;
        console.log('[RECONNECTION] Updated myPlayerId to new socket ID:', window.myPlayerId);
        
        // Also update the online game manager if it exists
        if (window.onlineGameManager) {
            window.onlineGameManager.myPlayerId = data.newPlayerId;
            console.log('[RECONNECTION] Updated onlineGameManager.myPlayerId');
        }
    }
    
    // Update game state
    if (data.gameState) {
        multiplayer.currentGameId = data.gameId;
        
        // Process the game state
        if (window.handleGameState && typeof window.handleGameState === 'function') {
            window.handleGameState(data.gameState);
        }
    }
    
    // Show notification
    showNotification('Riconnessione alla partita riuscita! Puoi continuare a giocare.', 3000, 'success');
    
    // Play success sound
    if (window.gameAudio && window.gameAudio.play) {
        window.gameAudio.play('bonus', 0.7);
    }
    
    // Mark that we are no longer disconnected
    window.wasDisconnected = false;
}

// Handle successful game state recovery
function handleRecoverySuccess(data) {
    console.log('[RECOVERY] Game state recovered:', data);

    // Show notification only if we were disconnessi e non stiamo avviando una nuova partita
    if (window.wasDisconnected) {
        // Hide any connection error message
        const connectionErrorElement = document.getElementById('connection-error');
        if (connectionErrorElement) {
            connectionErrorElement.style.display = 'none';
        }

        // Play success sound
        if (window.gameAudio && window.gameAudio.play) {
            window.gameAudio.play('bonus', 0.7);
        }

        // Se non stiamo avviando una nuova partita esplicitamente, mostra notifica di riconnessione
        if (localStorage.getItem('disconnected_game_state')) {
            // Show enhanced notification
            showNotification('Riconnessione alla partita riuscita! Puoi continuare a giocare.', 3000, 'success');
        }
        window.wasDisconnected = false;
    }
}

// Handle disconnection warning
function handleDisconnectionWarning(data) {
    console.log('[WARNING] Disconnection timeout approaching:', data);

    // Play warning sound
    if (window.gameAudio && window.gameAudio.play) {
        window.gameAudio.play('error', 0.5);
    }

    // Show warning notification with remaining time
    showNotification(`L'avversario sta per essere disconnesso. Tempo rimasto: ${data.remainingTime} secondi`, 6000);

    // Flash the reconnection overlay if it exists
    const overlay = document.getElementById('reconnection-overlay');
    if (overlay) {
        overlay.classList.add('warning-flash');

        // Update the countdown
        const countdownElement = document.getElementById('reconnection-countdown');
        if (countdownElement) {
            countdownElement.textContent = data.remainingTime;
            countdownElement.classList.add('warning-flash');
        }

        // Remove warning flash class after animation
        setTimeout(() => {
            overlay.classList.remove('warning-flash');
            if (countdownElement) {
                countdownElement.classList.remove('warning-flash');
            }
        }, 2000);
    }
}

// Get current user profile
function getCurrentUserProfile() {
    // Use authUtils.getCurrentUser() for consistency
    return window.authUtils?.getCurrentUser() || null;
}

// Get user rating
function getUserRating() {
    // Try to get from user profile
    const userProfile = getCurrentUserProfile();
    if (userProfile && userProfile.rating) {
        return userProfile.rating;
    }

    // Default rating
    return 1000;
}

// Check if user is logged in
function isUserLoggedIn() {
    // Verifica sia il token che l'oggetto user per garantire coerenza con authUtils.isLoggedIn()
    return !!(localStorage.getItem('token') && localStorage.getItem('user'));
}

// Show login prompt
function showLoginPrompt(message) {
    // Verifica se l'utente è già loggato secondo authUtils
    if (window.authUtils && window.authUtils.isLoggedIn()) {
        console.log('[LOGIN PROMPT] Utente già loggato secondo authUtils, ma non secondo isUserLoggedIn()');
        console.log('- Token presente:', !!localStorage.getItem('token'));
        console.log('- User presente:', !!localStorage.getItem('user'));

        // Mostra un messaggio di errore e suggerisci di ricaricare la pagina
        showError('Errore di sincronizzazione dello stato di login. Prova a ricaricare la pagina.');
        return;
    }

    // Save the current action in localStorage
    localStorage.setItem('pendingMultiplayerAction', 'startMatchmaking');

    // Create login prompt modal
    const loginPrompt = document.createElement('div');
    loginPrompt.className = 'login-prompt-modal';
    loginPrompt.innerHTML = `
        <div class="login-prompt-content">
            <h2>Accesso Richiesto</h2>
            <p>${message}</p>
            <div class="login-prompt-buttons">
                <button class="login-btn">Accedi</button>
                <button class="register-btn">Registrati</button>
                <button class="cancel-btn">Annulla</button>
            </div>
        </div>
    `;

    // Add to body
    document.body.appendChild(loginPrompt);

    // Show modal
    setTimeout(() => {
        loginPrompt.classList.add('show');
    }, 10);

    // Add button events
    const loginBtn = loginPrompt.querySelector('.login-btn');
    const registerBtn = loginPrompt.querySelector('.register-btn');
    const cancelBtn = loginPrompt.querySelector('.cancel-btn');

    if (loginBtn) {
        loginBtn.addEventListener('click', () => {
            // Add a return URL parameter to redirect back to the game after login
            window.location.href = '/login?returnTo=game';
        });
    }

    if (registerBtn) {
        registerBtn.addEventListener('click', () => {
            // Add a return URL parameter to redirect back to the game after registration
            window.location.href = '/register?returnTo=game';
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            // Clear the pending action
            localStorage.removeItem('pendingMultiplayerAction');

            loginPrompt.classList.remove('show');
            setTimeout(() => {
                loginPrompt.remove();
            }, 300);
        });
    }
}

// Show error message
function showError(message) {
    // Se il messaggio riguarda un errore di sincronizzazione, mostra un pulsante per ricaricare la pagina
    if (message.includes('sincronizzazione dello stato di login')) {
        // Crea una notifica speciale con pulsante di ricarica
        const notification = document.createElement('div');
        notification.className = 'notification error-notification';
        notification.innerHTML = `
            ${message}
            <button class="reload-btn">Ricarica Pagina</button>
        `;

        // Aggiungi al container
        if (notificationContainer) {
            notificationContainer.appendChild(notification);

            // Mostra notifica
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // Aggiungi event listener al pulsante
            const reloadBtn = notification.querySelector('.reload-btn');
            if (reloadBtn) {
                reloadBtn.addEventListener('click', () => {
                    window.location.reload();
                });
            }

            // Non rimuovere automaticamente questa notifica
        } else {
            // Fallback se il container non esiste
            alert(message + " Per favore, ricarica la pagina.");
        }
    } else {
        // Per altri errori, usa la notifica standard
        showNotification(message);
    }
}

// Get current user ID
function getCurrentUserId() {
    const userProfile = getCurrentUserProfile();
    return userProfile ? userProfile.id : null;
}

// Execute game action
function executeGameAction(action, actionData) {
    if (!multiplayer.currentGameId) return;

    // Send action to server
    socket.emit('gameAction', {
        action,
        gameId: multiplayer.currentGameId,
        actionData
    });
}

// Update game state with turn timer
function updateGameStateWithTurnTimer(gameState) {
    // Check if it's our turn
    const isMyTurn = gameState.currentPlayerId === gameState.myPlayerId;

    // Check if battle animation has completed
    const gameContainerElement = document.getElementById('game-container');
    const isGameReady = gameContainerElement && gameContainerElement.classList.contains('ready-for-play');

    // Update turn timer
    if (gameState.turnTimeRemaining !== undefined) {
        multiplayer.turnTimeout = gameState.turnTimeout || 60;

        // Only start timers if game is ready for play (animation completed)
        if (isGameReady) {
            if (isMyTurn) {
                // Start timer with remaining time
                startTurnTimer(gameState.turnTimeRemaining);
            } else {
                // Stop timer
                stopTurnTimer();
            }

            // Decrementa il timer totale del giocatore attivo
            if (window && window.currentGameState && window.player1TotalTime !== undefined && window.player2TotalTime !== undefined) {
                try {
                    // Determina di quale giocatore è il turno
                    const isPlayer1Turn = gameState.players && gameState.currentPlayerId &&
                                        gameState.players[gameState.currentPlayerId]?.color === 'white';
                    const isPlayer2Turn = gameState.players && gameState.currentPlayerId &&
                                        gameState.players[gameState.currentPlayerId]?.color === 'black';

                    // Aggiorna i timer totali (30 minuti) per il giocatore corrente
                    if (isPlayer1Turn) {
                        if (!window.player1TimerInterval) {
                            console.log('[TIMER] Avviato timer totale per giocatore 1');
                            window.player1TimerInterval = setInterval(() => {
                                // Verifica ancora che l'animazione sia completata
                                const gameContainerElement = document.getElementById('game-container');
                                const isGameReady = gameContainerElement && gameContainerElement.classList.contains('ready-for-play');

                                if (isGameReady) {
                                    if (window.player1TotalTime > 0) {
                                        window.player1TotalTime--;

                                        // Aggiorna il display
                                        const player1TotalTimerCountElement = document.querySelector('#player1-total-timer .total-timer-count');
                                        if (player1TotalTimerCountElement) {
                                            const minutes = Math.floor(window.player1TotalTime / 60);
                                            const seconds = window.player1TotalTime % 60;
                                            player1TotalTimerCountElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                                        }

                                        // Avvisi per tempo rimanente basso per il giocatore bianco
                                        if (window.player1TotalTime === 300) { // 5 minuti
                                            showNotification('Ti rimangono 5 minuti!', 3000, 'warning');
                                        } else if (window.player1TotalTime === 60) { // 1 minuto
                                            showNotification('Ultimo minuto!', 3000, 'warning');
                                        }
                                    } else if (window.player1TotalTime === 0) {
                                        // Tempo esaurito
                                        clearInterval(window.player1TimerInterval);
                                        window.player1TimerInterval = null;

                                        // Trova il nome dell'avversario
                                        let opponentName = 'Avversario';
                                        if (window.currentGameState && window.currentGameState.players) {
                                            const opponentId = Object.keys(window.currentGameState.players).find(id => id !== window.currentGameState.myPlayerId);
                                            if (opponentId && window.currentGameState.playerNames && window.currentGameState.playerNames[opponentId]) {
                                                opponentName = window.currentGameState.playerNames[opponentId];
                                            } else if (window.multiplayer && window.multiplayer.opponent && window.multiplayer.opponent.name) {
                                                opponentName = window.multiplayer.opponent.name;
                                            }
                                        }

                                        // Mostra il riquadro di vittoria per l'avversario
                                        if (window.showVictoryScreen) {
                                            window.showVictoryScreen(opponentName, 'Vittoria per tempo scaduto!');
                                        }

                                        // Il server si occuperà di inviare l'evento gameOver
                                    }
                                }
                            }, 1000);
                        }

                        // Ferma il timer dell'altro giocatore
                        if (window.player2TimerInterval) {
                            clearInterval(window.player2TimerInterval);
                            window.player2TimerInterval = null;
                        }
                    } else if (isPlayer2Turn) {
                        if (!window.player2TimerInterval) {
                            console.log('[TIMER] Avviato timer totale per giocatore 2');
                            window.player2TimerInterval = setInterval(() => {
                                // Verifica ancora che l'animazione sia completata
                                const gameContainerElement = document.getElementById('game-container');
                                const isGameReady = gameContainerElement && gameContainerElement.classList.contains('ready-for-play');

                                if (isGameReady) {
                                    if (window.player2TotalTime > 0) {
                                        window.player2TotalTime--;

                                        // Aggiorna il display
                                        const player2TotalTimerCountElement = document.querySelector('#player2-total-timer .total-timer-count');
                                        if (player2TotalTimerCountElement) {
                                            const minutes = Math.floor(window.player2TotalTime / 60);
                                            const seconds = window.player2TotalTime % 60;
                                            player2TotalTimerCountElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                                        }

                                        // Avvisi per tempo rimanente basso per il giocatore nero
                                        if (window.player2TotalTime === 300) { // 5 minuti
                                            showNotification('L\'avversario ha 5 minuti rimanenti', 3000, 'info');
                                        } else if (window.player2TotalTime === 60) { // 1 minuto
                                            showNotification('L\'avversario ha un minuto rimanente', 3000, 'info');
                                        }
                                    } else if (window.player2TotalTime === 0) {
                                        // Tempo esaurito
                                        clearInterval(window.player2TimerInterval);
                                        window.player2TimerInterval = null;

                                        // Trova il tuo nome
                                        let playerName = 'Giocatore';
                                        if (window.currentGameState && window.currentGameState.myPlayerId &&
                                            window.currentGameState.playerNames && window.currentGameState.playerNames[window.currentGameState.myPlayerId]) {
                                            playerName = window.currentGameState.playerNames[window.currentGameState.myPlayerId];
                                        } else if (localStorage.getItem('user')) {
                                            try {
                                                const userData = JSON.parse(localStorage.getItem('user'));
                                                if (userData && userData.username) {
                                                    playerName = userData.username;
                                                }
                                            } catch (e) {
                                                console.error('Errore nel parsing dei dati utente:', e);
                                            }
                                        }

                                        // Mostra il riquadro di vittoria
                                        if (window.showVictoryScreen) {
                                            window.showVictoryScreen(playerName, 'Vittoria per tempo scaduto!');
                                        }

                                        // Il server si occuperà di inviare l'evento gameOver
                                    }
                                }
                            }, 1000);
                        }

                        // Ferma il timer dell'altro giocatore
                        if (window.player1TimerInterval) {
                            clearInterval(window.player1TimerInterval);
                            window.player1TimerInterval = null;
                        }
                    }
                } catch (error) {
                    console.error('[TIMER] Errore nell\'aggiornamento dei timer totali:', error);
                }
            }
        } else {
            console.log('[TIMER] La partita non è ancora pronta (animazione in corso). I timer partiranno dopo l\'animazione.');
            // Stop any existing timers while animation is running
            stopTurnTimer();
            if (window.player1TimerInterval) {
                clearInterval(window.player1TimerInterval);
                window.player1TimerInterval = null;
            }
            if (window.player2TimerInterval) {
                clearInterval(window.player2TimerInterval);
                window.player2TimerInterval = null;
            }
        }
    }

    // Aggiorna i rating dei giocatori se l'avversario è disponibile
    try {
        if (multiplayer.opponent && multiplayer.opponent.rating) {
            const player2RatingElement = document.getElementById('player2-rating');
            if (player2RatingElement) {
                player2RatingElement.textContent = `(${multiplayer.opponent.rating})`;
                console.log('[GAMESTATE] Rating giocatore 2 aggiornato:', multiplayer.opponent.rating);
            }
        }
    } catch (error) {
        console.error('[GAMESTATE] Errore nell\'aggiornamento del rating del giocatore 2:', error);
    }

    return gameState;
}

// Initialize on page load
/**
 * Crea gli slot vuoti per le carte nelle aree delle mani dei giocatori
 * Questa funzione assicura che ci siano sempre 10 slot (2 righe da 5) visibili
 * nelle aree delle mani dei giocatori in modalità multiplayer
 */
function createHandSlots() {
    console.log('[HAND SLOTS] Creazione slot per le carte nelle aree delle mani');
    
    // Configurazione
    const MAX_SLOTS = 10; // 2 righe da 5 slot
    const SLOTS_PER_ROW = 5;
    
    // Elemento della mano del giocatore 1 (bianco)
    const player1HandElement = document.getElementById('player1-hand');
    if (player1HandElement) {
        // Pulisci l'area della mano
        player1HandElement.innerHTML = '';
        
        // Crea gli slot vuoti
        for (let i = 0; i < MAX_SLOTS; i++) {
            const slot = document.createElement('div');
            slot.className = 'card-slot empty-slot';
            slot.dataset.index = i;
            slot.setAttribute('role', 'button');
            slot.setAttribute('aria-label', `Slot carta ${i+1} del giocatore 1`);
            player1HandElement.appendChild(slot);
        }
        console.log('[HAND SLOTS] Creati slot per la mano del giocatore 1');
    } else {
        console.warn('[HAND SLOTS] Elemento player1-hand non trovato');
    }
    
    // Elemento della mano del giocatore 2 (nero)
    const player2HandElement = document.getElementById('player2-hand');
    if (player2HandElement) {
        // Pulisci l'area della mano
        player2HandElement.innerHTML = '';
        
        // Crea gli slot vuoti
        for (let i = 0; i < MAX_SLOTS; i++) {
            const slot = document.createElement('div');
            slot.className = 'card-slot empty-slot';
            slot.dataset.index = i;
            slot.setAttribute('role', 'button');
            slot.setAttribute('aria-label', `Slot carta ${i+1} del giocatore 2`);
            player2HandElement.appendChild(slot);
        }
        console.log('[HAND SLOTS] Creati slot per la mano del giocatore 2');
    } else {
        console.warn('[HAND SLOTS] Elemento player2-hand non trovato');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Pulisci l'URL se siamo arrivati con parametri
    if (window.location.search) {
        // Rimuovi i parametri dall'URL usando History API
        window.history.replaceState({}, document.title, '/game');
    }
    
    // Inizializza le funzionalità multiplayer
    initMultiplayer();
    
    // Crea gli slot per le carte nelle aree delle mani
    // lo facciamo subito per evitare che le aree appaiano vuote
    createHandSlots();
    
    // Configura un osservatore che monitori il DOM per ricreare gli slot delle mani 
    // quando vengono cancellati o quando il contenitore delle mani viene modificato
    const handAreasObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                // Verifica se le aree delle mani sono vuote o hanno meno di 10 slot
                const player1HandElement = document.getElementById('player1-hand');
                const player2HandElement = document.getElementById('player2-hand');
                
                if (player1HandElement && player1HandElement.children.length < 10) {
                    console.log('[OBSERVER] Rilevata modifica dell\'area della mano del giocatore 1, ricreo gli slot');
                    createHandSlots();
                }
                
                if (player2HandElement && player2HandElement.children.length < 10) {
                    console.log('[OBSERVER] Rilevata modifica dell\'area della mano del giocatore 2, ricreo gli slot');
                    createHandSlots();
                }
            }
        });
    });
    
    // Osserva le aree delle mani
    const player1HandElement = document.getElementById('player1-hand');
    const player2HandElement = document.getElementById('player2-hand');
    
    if (player1HandElement) {
        handAreasObserver.observe(player1HandElement, { childList: true });
    }
    
    if (player2HandElement) {
        handAreasObserver.observe(player2HandElement, { childList: true });
    }

    // Aggiungi variabili globali per i timer dei giocatori
    if (typeof window.player1TimerInterval === 'undefined') window.player1TimerInterval = null;
    if (typeof window.player2TimerInterval === 'undefined') window.player2TimerInterval = null;

    // Assicurati che i timer totali siano inizializzati (fallback se non sono definiti in script.js)
    if (typeof window.player1TotalTime === 'undefined') window.player1TotalTime = 1800; // 30 minuti
    if (typeof window.player2TotalTime === 'undefined') window.player2TotalTime = 1800; // 30 minuti

    // Log per debug
    // console.log('[TIMER INIT] Inizializzati timer globali per i giocatori');

    // Aggiorna immediatamente il display dei timer totali
    setTimeout(() => {
        const player1TotalTimerCountElement = document.querySelector('#player1-total-timer .total-timer-count');
        const player2TotalTimerCountElement = document.querySelector('#player2-total-timer .total-timer-count');

        if (player1TotalTimerCountElement) {
            const minutes1 = Math.floor(window.player1TotalTime / 60);
            const seconds1 = window.player1TotalTime % 60;
            player1TotalTimerCountElement.textContent = `${minutes1.toString().padStart(2, '0')}:${seconds1.toString().padStart(2, '0')}`;
        }

        if (player2TotalTimerCountElement) {
            const minutes2 = Math.floor(window.player2TotalTime / 60);
            const seconds2 = window.player2TotalTime % 60;
            player2TotalTimerCountElement.textContent = `${minutes2.toString().padStart(2, '0')}:${seconds2.toString().padStart(2, '0')}`;
        }
    }, 500);

    // Monitor game-container for the 'ready-for-play' class being added
    // When detected, refresh the game state to start timers
    const gameContainerObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' &&
                mutation.attributeName === 'class' &&
                mutation.target.classList.contains('ready-for-play')) {

                // console.log('[ANIMATION] Rilevata classe ready-for-play sul game-container, avvio i timer');

                // If we have a current game state, re-apply it to start timers
                if (window.currentGameState) {
                    // Wait a short moment to ensure animation is fully complete
                    setTimeout(() => {
                        // console.log('[ANIMATION] Riapplico lo stato di gioco per avviare i timer dopo l\'animazione');
                        updateGameStateWithTurnTimer(window.currentGameState);
                    }, 100);
                }

                // Disconnect observer as we only need this once
                gameContainerObserver.disconnect();
            }
        });
    });

    // Start observing game-container for class changes
    const gameContainer = document.getElementById('game-container');
    if (gameContainer) {
        gameContainerObserver.observe(gameContainer, { attributes: true });
        // console.log('[ANIMATION] Osservatore impostato per rilevare il completamento dell\'animazione');
    } else {
        // console.log('[ANIMATION] Elemento game-container non trovato, impossibile monitorare l\'animazione');
    }
});

// Funzione per aggiornare direttamente il rating dell'avversario nella UI
function updateOpponentRatingDisplay() {
    if (!multiplayer.opponent || !multiplayer.opponent.rating) {
        console.log('[RATING] Nessun opponent con rating disponibile');
        return;
    }

    const player2RatingElement = document.getElementById('player2-rating');
    if (player2RatingElement) {
        player2RatingElement.textContent = `(${multiplayer.opponent.rating})`;
        console.log('[RATING] Rating avversario aggiornato nella UI:', multiplayer.opponent.rating);
    } else {
        console.error('[RATING] Elemento player2-rating non trovato!');

        // Tenta con un selettore alternativo
        const alternative = document.querySelector('#player2-area .player-rating');
        if (alternative) {
            alternative.textContent = `(${multiplayer.opponent.rating})`;
            console.log('[RATING] Rating avversario aggiornato con selettore alternativo');
        }
    }
}

// Funzioni per la chat
let chatInput;
let emojiButton;
let emojiPicker;
let chatMessages;

// Inizializza gli elementi della chat
function initChat() {
    // console.log('[CHAT] Inizializzazione chat multiplayer');

    // Ottieni riferimenti agli elementi
    chatInput = document.getElementById('chat-input');
    emojiButton = document.getElementById('emoji-button');
    emojiPicker = document.getElementById('emoji-picker');
    chatMessages = document.getElementById('chat-messages');

    // La chat sarà sempre visibile nella sidebar, non nascondiamola all'inizio
    // ma aggiorniamo la sua visibilità in base allo stato del gioco
    updateChatVisibility();

    // Assicuriamoci che l'event listener per chatMessage sia registrato qui
    // (in caso il listener in initSocketEvents non fosse stato attivato)
    if (socket) {
        socket.off('chatMessage'); // Rimuovi eventuali listener duplicati
        socket.on('chatMessage', function(data) {
            console.log('[CHAT] DEBUG: Ricevuto messaggio in initChat:', data);
            handleChatMessage(data);
        });
        // console.log('[CHAT] Event listener per chatMessage registrato in initChat');
    }

    // Inizializza gli event listener
    if (chatInput) {
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && chatInput.value.trim() !== '') {
                const trimmedMessage = chatInput.value.trim();
                console.log('[CHAT] Invio messaggio dalla tastiera:', trimmedMessage);
                sendChatMessage(trimmedMessage);
                chatInput.value = '';
            }
        });

        // Aggiungi anche un event listener per click su invio
        const chatForm = chatInput.closest('form');
        if (!chatForm) {
            // Se non c'è un form parent, creiamo un bottone di invio
            const sendButton = document.createElement('button');
            sendButton.type = 'button';
            sendButton.className = 'send-button';
            sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
            sendButton.title = 'Invia messaggio';

            // Inseriamo il bottone prima dell'emoji button
            const emojiButton = document.getElementById('emoji-button');
            if (emojiButton && emojiButton.parentNode) {
                emojiButton.parentNode.insertBefore(sendButton, emojiButton);

                // Aggiungi event listener al bottone
                sendButton.addEventListener('click', function() {
                    if (chatInput.value.trim() !== '') {
                        const trimmedMessage = chatInput.value.trim();
                        console.log('[CHAT] Invio messaggio dal bottone:', trimmedMessage);
                        sendChatMessage(trimmedMessage);
                        chatInput.value = '';
                        chatInput.focus();
                    }
                });
            }
        }
    }

    if (emojiButton) {
        emojiButton.addEventListener('click', function() {
            toggleEmojiPicker();
        });
    }

    // Aggiungi event listener per gli emoji
    const emojiItems = document.querySelectorAll('.emoji-item');
    emojiItems.forEach(emoji => {
        emoji.addEventListener('click', function() {
            insertEmoji(emoji.textContent);
            toggleEmojiPicker();
        });
    });

    // Chiudi emoji picker quando si clicca altrove
    document.addEventListener('click', function(e) {
        if (emojiPicker.classList.contains('active') &&
            !emojiPicker.contains(e.target) &&
            e.target !== emojiButton) {
            emojiPicker.classList.remove('active');
        }
    });

    // Aggiungi event listener per i messaggi della chat
    socket.on('chatMessage', handleChatMessage);

    // console.log('[CHAT] Chat multiplayer inizializzata');
}

// Mostra/nascondi emoji picker
function toggleEmojiPicker() {
    if (emojiPicker) {
        emojiPicker.classList.toggle('active');
    }
}

// Inserisci emoji nella casella di input
function insertEmoji(emoji) {
    if (chatInput) {
        chatInput.value += emoji;
        chatInput.focus();
    }
}

// Invia messaggio chat al server
function sendChatMessage(message) {
    if (!socket || !socket.connected || !multiplayer.currentGameId) {
        showError('Impossibile inviare il messaggio: connessione al server non disponibile');
        return;
    }

    console.log('[CHAT] Invio messaggio chat:', message);
    socket.emit('chatMessage', {
        gameId: multiplayer.currentGameId,
        message: message
    });

    // Aggiungi subito il messaggio nella chat locale (ottimistic UI)
    const userName = window.authUtils?.getCurrentUser()?.username || 'Tu';
    addMessageToChat(message, userName, true);
}

// Gestisci i messaggi ricevuti
function handleChatMessage(data) {
    console.log('[CHAT] Ricevuto messaggio chat:', data);

    // Se non è un messaggio inviato da noi, aggiungi alla chat
    if (data.senderId !== socket.id) {
        // Log aggiuntivo per debug
        console.log('[CHAT] Visualizzando messaggio dall\'avversario:', data.senderName, data.message);
        addMessageToChat(data.message, data.senderName, false);

        // Riproduci un suono di notifica quando arriva un messaggio
        if (window.gameAudio && window.gameAudio.play) {
            window.gameAudio.play('card', 0.2); // Usa un suono esistente a basso volume
        }
    }
}

// Aggiungi messaggio alla chat
function addMessageToChat(message, senderName, isSelf) {
    if (!chatMessages) return;

    const messageElement = document.createElement('div');
    messageElement.className = `chat-message ${isSelf ? 'self' : 'other'}`;

    // Per i messaggi propri, colore blu
    // Per i messaggi altrui, colore verde
    const nameColor = isSelf ? '#3498db' : '#27ae60';

    const nameSpan = document.createElement('span');
    nameSpan.className = 'sender-name';
    nameSpan.textContent = senderName + ': ';
    nameSpan.style.color = nameColor;

    const messageText = document.createElement('span');
    messageText.className = 'message-text';
    messageText.textContent = message;

    messageElement.appendChild(nameSpan);
    messageElement.appendChild(messageText);

    chatMessages.appendChild(messageElement);

    // Scroll verso il basso per mostrare il nuovo messaggio
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Evidenzia brevemente il messaggio per attirare l'attenzione
    setTimeout(() => {
        messageElement.classList.add('highlight');
        setTimeout(() => {
            messageElement.classList.remove('highlight');
        }, 1000);
    }, 100);
}

// Mostra/nascondi chat in base allo stato multiplayer
function updateChatVisibility() {
    const chatArea = document.getElementById('chat-area');
    // console.log('[CHAT] Aggiornamento visibilità chat. gameId:', multiplayer.currentGameId);

    if (chatArea) {
        // La chat è sempre visibile nella sidebar quando siamo nella schermata di gioco
        chatArea.style.display = 'block';

        // Se siamo in multiplayer, aggiungiamo una classe per indicarlo
        if (multiplayer.currentGameId) {
            chatArea.classList.add('multiplayer-active');
            console.log('[CHAT] Chat abilitata per il multiplayer');
        } else {
            chatArea.classList.remove('multiplayer-active');
        }
    }
}

// Aggiungi inizializzazione chat al DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    // Inizializza le funzionalità multiplayer
    initMultiplayer();

    // Inizializza la chat
    initChat();

    // Aggiungi variabili globali per i timer dei giocatori
    if (typeof window.player1TimerInterval === 'undefined') window.player1TimerInterval = null;
    if (typeof window.player2TimerInterval === 'undefined') window.player2TimerInterval = null;

    // Assicurati che i timer totali siano inizializzati (fallback se non sono definiti in script.js)
    if (typeof window.player1TotalTime === 'undefined') window.player1TotalTime = 1800; // 30 minuti
    if (typeof window.player2TotalTime === 'undefined') window.player2TotalTime = 1800; // 30 minuti

    // Log per debug
    // console.log('[TIMER INIT] Inizializzati timer globali per i giocatori');

    // Aggiorna immediatamente il display dei timer totali
    setTimeout(() => {
        const player1TotalTimerCountElement = document.querySelector('#player1-total-timer .total-timer-count');
        const player2TotalTimerCountElement = document.querySelector('#player2-total-timer .total-timer-count');

        if (player1TotalTimerCountElement) {
            const minutes1 = Math.floor(window.player1TotalTime / 60);
            const seconds1 = window.player1TotalTime % 60;
            player1TotalTimerCountElement.textContent = `${minutes1.toString().padStart(2, '0')}:${seconds1.toString().padStart(2, '0')}`;
        }

        if (player2TotalTimerCountElement) {
            const minutes2 = Math.floor(window.player2TotalTime / 60);
            const seconds2 = window.player2TotalTime % 60;
            player2TotalTimerCountElement.textContent = `${minutes2.toString().padStart(2, '0')}:${seconds2.toString().padStart(2, '0')}`;
        }
    }, 500);

    // Monitor game-container for the 'ready-for-play' class being added
    // When detected, refresh the game state to start timers
    const gameContainerObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' &&
                mutation.attributeName === 'class' &&
                mutation.target.classList.contains('ready-for-play')) {

                // console.log('[ANIMATION] Rilevata classe ready-for-play sul game-container, avvio i timer');

                // If we have a current game state, re-apply it to start timers
                if (window.currentGameState) {
                    // Wait a short moment to ensure animation is fully complete
                    setTimeout(() => {
                        // console.log('[ANIMATION] Riapplico lo stato di gioco per avviare i timer dopo l\'animazione');
                        updateGameStateWithTurnTimer(window.currentGameState);
                    }, 100);
                }

                // Disconnect observer as we only need this once
                gameContainerObserver.disconnect();
            }
        });
    });

    // Start observing game-container for class changes
    const gameContainer = document.getElementById('game-container');
    if (gameContainer) {
        gameContainerObserver.observe(gameContainer, { attributes: true });
        // console.log('[ANIMATION] Osservatore impostato per rilevare il completamento dell\'animazione');
    } else {
        // console.log('[ANIMATION] Elemento game-container non trovato, impossibile monitorare l\'animazione');
    }
});

// Export functions for use in other scripts
window.multiplayer = {
    startMatchmaking,
    cancelMatchmaking,
    createPrivateGame,
    joinPrivateGame,
    executeGameAction,
    updateGameStateWithTurnTimer,
    showError,
    showNotification,
    updateOpponentRatingDisplay,
    startTurnTimer,
    stopTurnTimer,
    // handleGameState è gestito in script.js
    // Chat functions
    sendChatMessage,
    // Esponi anche queste variabili per debugging
    state: multiplayer
};

// Rendi createHandSlots disponibile globalmente per permettere ad altri script di utilizzarla
window.createHandSlots = createHandSlots;
