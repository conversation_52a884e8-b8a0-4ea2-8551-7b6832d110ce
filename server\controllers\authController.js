const User = require('../database/models/User');
const security = require('../utils/security');
const emailService = require('../utils/email');
const jwt = require('jsonwebtoken');

// JWT secret key (should be in environment variables in production)
const JWT_SECRET = process.env.JWT_SECRET || 'skemino_secret_key';
const JWT_EXPIRES_IN = '7d'; // 7 days

/**
 * Authentication controller
 */
const authController = {
    /**
     * Register a new user
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async register(req, res) {
        try {
            console.log('Registration request received');
            console.log('Request headers:', req.headers);
            console.log('Request body:', req.body);

            // Check if the request body is empty or not properly parsed
            if (!req.body || Object.keys(req.body).length === 0) {
                console.log('Empty or invalid request body');
                return res.status(400).json({
                    success: false,
                    message: 'Richiesta non valida: corpo della richiesta vuoto o non valido',
                    debug: {
                        bodyType: typeof req.body,
                        contentType: req.headers['content-type']
                    }
                });
            }

            // Extract data from request body (support both JSON and FormData)
            let username, email, password, confirmPassword;

            if (req.headers['content-type'] && req.headers['content-type'].includes('multipart/form-data')) {
                // FormData
                username = req.body.username;
                email = req.body.email;
                password = req.body.password;
                confirmPassword = req.body.confirmPassword;
            } else {
                // JSON
                ({ username, email, password, confirmPassword } = req.body);
            }

            console.log('Extracted data:', {
                username,
                email,
                password: password ? '***' : undefined,
                confirmPassword: confirmPassword ? '***' : undefined
            });

            // Validate input
            if (!username || !email || !password || !confirmPassword) {
                console.log('Missing required fields:', {
                    hasUsername: !!username,
                    hasEmail: !!email,
                    hasPassword: !!password,
                    hasConfirmPassword: !!confirmPassword
                });

                return res.status(400).json({
                    success: false,
                    message: 'Tutti i campi sono obbligatori',
                    missingFields: {
                        username: !username,
                        email: !email,
                        password: !password,
                        confirmPassword: !confirmPassword
                    }
                });
            }

            // Validate username
            const usernameValidation = security.validateUsername(username);
            if (!usernameValidation.valid) {
                return res.status(400).json({
                    success: false,
                    message: usernameValidation.message
                });
            }

            // Validate email
            const emailValidation = security.validateEmail(email);
            if (!emailValidation.valid) {
                return res.status(400).json({
                    success: false,
                    message: emailValidation.message
                });
            }

            // Validate password
            const passwordValidation = security.validatePassword(password);
            if (!passwordValidation.valid) {
                return res.status(400).json({
                    success: false,
                    message: passwordValidation.message
                });
            }

            // Check if passwords match
            if (password !== confirmPassword) {
                return res.status(400).json({
                    success: false,
                    message: 'Le password non corrispondono'
                });
            }

            // Check if username already exists
            try {
                console.log('Checking if username exists:', username);
                const existingUsername = await User.findByUsername(username);
                console.log('Username check result:', existingUsername);

                if (existingUsername) {
                    return res.status(400).json({
                        success: false,
                        message: 'Nome utente già in uso',
                        debug: { usernameExists: true, username }
                    });
                }
            } catch (usernameError) {
                console.error('Error checking username:', usernameError);
                return res.status(500).json({
                    success: false,
                    message: 'Errore durante la verifica del nome utente',
                    error: usernameError.message,
                    stack: usernameError.stack
                });
            }

            // Check if email already exists
            try {
                console.log('Checking if email exists:', email);
                const existingEmail = await User.findByEmail(email);
                console.log('Email check result:', existingEmail);

                if (existingEmail) {
                    return res.status(400).json({
                        success: false,
                        message: 'Email già in uso',
                        debug: { emailExists: true, email }
                    });
                }
            } catch (emailError) {
                console.error('Error checking email:', emailError);
                return res.status(500).json({
                    success: false,
                    message: 'Errore durante la verifica dell\'email',
                    error: emailError.message,
                    stack: emailError.stack
                });
            }

            // Generate salt and hash password
            const salt = await security.generateSalt();
            const hashedPassword = await security.hashPassword(password, salt);

            // Generate activation token
            const activationToken = await security.generateToken();

            // Create user
            try {
                console.log('Creating user with data:', {
                    username,
                    email,
                    passwordLength: hashedPassword ? hashedPassword.length : 0,
                    saltLength: salt ? salt.length : 0,
                    activationTokenLength: activationToken ? activationToken.length : 0
                });

                const userData = {
                    username,
                    email,
                    password: hashedPassword,
                    salt,
                    activationToken
                };

                const result = await User.create(userData);
                console.log('User creation result:', result);

                if (!result || !result.success) {
                    return res.status(500).json({
                        success: false,
                        message: 'Errore durante la registrazione',
                        debug: { result }
                    });
                }

                // In development mode, activate the account directly without email
                if (process.env.NODE_ENV !== 'production' || req.query.dev === 'true') {
                    // Activate the account directly
                    await User.activate(result.userId);

                    res.status(201).json({
                        success: true,
                        message: 'Registrazione completata e account attivato automaticamente (modalità sviluppo).',
                        devInfo: {
                            activationToken,
                            activationLink: `http://localhost:3000/api/auth/activate/${activationToken}`,
                            autoActivated: true,
                            userId: result.userId
                        }
                    });
                } else {
                    // Send activation email in production
                    try {
                        await emailService.sendActivationEmail(email, username, activationToken);

                        res.status(201).json({
                            success: true,
                            message: 'Registrazione completata. Controlla la tua email per attivare l\'account.'
                        });
                    } catch (emailError) {
                        console.error('Error sending activation email:', emailError);

                        // In development mode, we'll still consider the registration successful
                        // and provide the activation token directly to the user for testing
                        if (process.env.NODE_ENV !== 'production') {
                            res.status(201).json({
                                success: true,
                                message: 'Registrazione completata. In modalità sviluppo, l\'email non è stata inviata.',
                                devInfo: {
                                    activationToken,
                                    activationLink: `http://localhost:3000/api/auth/activate/${activationToken}`
                                }
                            });
                        } else {
                            // In production, we'll still consider it a success but log the error
                            res.status(201).json({
                                success: true,
                                message: 'Registrazione completata. Se non ricevi l\'email di attivazione, contatta il supporto.'
                            });
                        }
                    }
                }
            } catch (createError) {
                console.error('Error creating user:', createError);
                return res.status(500).json({
                    success: false,
                    message: 'Errore durante la creazione dell\'utente',
                    error: createError.message,
                    stack: createError.stack
                });
            }
        } catch (error) {
            console.error('Registration error:', error);
            console.error('Error stack:', error.stack);

            // Send more detailed error in development
            if (process.env.NODE_ENV !== 'production') {
                res.status(500).json({
                    success: false,
                    message: 'Errore del server durante la registrazione',
                    error: error.message,
                    stack: error.stack
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: 'Errore del server durante la registrazione'
                });
            }
        }
    },

    /**
     * Activate a user account
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async activate(req, res) {
        try {
            const { token } = req.params;

            // Find user by activation token
            const user = await User.findByActivationToken(token);

            if (!user) {
                return res.status(400).json({
                    success: false,
                    message: 'Token di attivazione non valido o scaduto'
                });
            }

            // Activate user
            const activated = await User.activate(user.id);

            if (!activated) {
                return res.status(500).json({
                    success: false,
                    message: 'Errore durante l\'attivazione dell\'account'
                });
            }

            // Redirect to login page with success message
            res.redirect('/login?activated=true');
        } catch (error) {
            console.error('Activation error:', error);
            res.status(500).json({
                success: false,
                message: 'Errore del server durante l\'attivazione'
            });
        }
    },

    /**
     * Login a user
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async login(req, res) {
        try {
            const { username, password, rememberMe } = req.body;
            const ipAddress = req.ip;
            const userAgent = req.headers['user-agent'];

            // Validate input
            if (!username || !password) {
                return res.status(400).json({
                    success: false,
                    message: 'Username e password sono obbligatori'
                });
            }

            // Find user
            const user = await User.findByUsername(username);

            // Log login attempt
            const logStatus = user ? 'ATTEMPT' : 'FAILED_UNKNOWN_USER';
            if (user) {
                await User.logLogin({
                    userId: user.id,
                    ipAddress,
                    userAgent,
                    status: logStatus
                });
            }

            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Credenziali non valide'
                });
            }

            // Check if account is active
            if (!user.is_active) {
                return res.status(401).json({
                    success: false,
                    message: 'Account non attivato. Controlla la tua email.'
                });
            }

            // Check if account is locked
            const isLocked = await User.isLocked(user.id);
            if (isLocked) {
                return res.status(401).json({
                    success: false,
                    message: 'Account temporaneamente bloccato. Riprova più tardi.'
                });
            }

            // Verify password
            const isPasswordValid = await security.verifyPassword(password, user.password, user.salt);

            if (!isPasswordValid) {
                // Increment login attempts
                await User.incrementLoginAttempts(user.id);

                // Log failed login
                await User.logLogin({
                    userId: user.id,
                    ipAddress,
                    userAgent,
                    status: 'FAILED_WRONG_PASSWORD'
                });

                return res.status(401).json({
                    success: false,
                    message: 'Credenziali non valide'
                });
            }

            // Update last login
            await User.updateLastLogin(user.id);

            // Log successful login
            await User.logLogin({
                userId: user.id,
                ipAddress,
                userAgent,
                status: 'SUCCESS'
            });

            // Generate JWT token
            console.log('[AUTH DEBUG] Generazione token con JWT_SECRET:', JWT_SECRET);
            const token = jwt.sign(
                {
                    id: user.id,
                    username: user.username
                },
                JWT_SECRET,
                {
                    expiresIn: rememberMe ? '30d' : JWT_EXPIRES_IN
                }
            );

            // Set cookie options
            const cookieOptions = {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict'
            };

            // Add expiration if rememberMe is true
            if (rememberMe) {
                cookieOptions.maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
            }

            // Set JWT cookie
            res.cookie('token', token, cookieOptions);

            res.status(200).json({
                success: true,
                message: 'Login effettuato con successo',
                token: token, // Include il token nella risposta JSON
                user: {
                    id: user.id,
                    username: user.username,
                    rank: user.rank
                }
            });
        } catch (error) {
            console.error('Login error:', error);
            res.status(500).json({
                success: false,
                message: 'Errore del server durante il login'
            });
        }
    },

    /**
     * Logout a user
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    logout(req, res) {
        // Clear JWT cookie
        res.clearCookie('token');

        res.status(200).json({
            success: true,
            message: 'Logout effettuato con successo'
        });
    },

    /**
     * Request password reset
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async requestPasswordReset(req, res) {
        try {
            const { email } = req.body;

            // Validate input
            if (!email) {
                return res.status(400).json({
                    success: false,
                    message: 'Email obbligatoria'
                });
            }

            // Find user by email
            const user = await User.findByEmail(email);

            // Don't reveal if email exists or not for security
            if (!user) {
                return res.status(200).json({
                    success: true,
                    message: 'Se l\'email è registrata, riceverai istruzioni per reimpostare la password.'
                });
            }

            // Generate reset token
            const resetToken = await security.generateToken();

            // Save reset token
            await User.setResetToken(user.id, resetToken);

            // Send password reset email
            const emailResult = await emailService.sendPasswordResetEmail(email, user.username, resetToken);

            // Log email result for debugging
            console.log('Password reset email result:', emailResult);

            res.status(200).json({
                success: true,
                message: 'Se l\'email è registrata, riceverai istruzioni per reimpostare la password.'
            });
        } catch (error) {
            console.error('Password reset request error:', error);
            res.status(500).json({
                success: false,
                message: 'Errore del server durante la richiesta di reset password'
            });
        }
    },

    /**
     * Reset password
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async resetPassword(req, res) {
        try {
            const { token } = req.params;
            const { password, confirmPassword } = req.body;

            // Validate input
            if (!password || !confirmPassword) {
                return res.status(400).json({
                    success: false,
                    message: 'Tutti i campi sono obbligatori'
                });
            }

            // Validate password
            const passwordValidation = security.validatePassword(password);
            if (!passwordValidation.valid) {
                return res.status(400).json({
                    success: false,
                    message: passwordValidation.message
                });
            }

            // Check if passwords match
            if (password !== confirmPassword) {
                return res.status(400).json({
                    success: false,
                    message: 'Le password non corrispondono'
                });
            }

            // Find user by reset token
            const user = await User.findByResetToken(token);

            if (!user) {
                return res.status(400).json({
                    success: false,
                    message: 'Token non valido o scaduto'
                });
            }

            // Generate new salt and hash password
            const salt = await security.generateSalt();
            const hashedPassword = await security.hashPassword(password, salt);

            // Update password
            const updated = await User.updatePassword(user.id, hashedPassword, salt);

            if (!updated) {
                return res.status(500).json({
                    success: false,
                    message: 'Errore durante l\'aggiornamento della password'
                });
            }

            res.status(200).json({
                success: true,
                message: 'Password aggiornata con successo. Ora puoi effettuare il login.'
            });
        } catch (error) {
            console.error('Password reset error:', error);
            res.status(500).json({
                success: false,
                message: 'Errore del server durante il reset della password'
            });
        }
    },

    /**
     * Get current user
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    getCurrentUser(req, res) {
        // User is already attached to req by auth middleware
        const { user } = req;

        res.status(200).json({
            success: true,
            user: {
                id: user.id,
                username: user.username,
                rank: user.rank
            }
        });
    },

    /**
     * Check if username is available
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async checkUsername(req, res) {
        try {
            const { username } = req.query;

            // Validate input
            if (!username) {
                return res.status(400).json({
                    available: false,
                    message: 'Nome utente obbligatorio'
                });
            }

            // Validate username format
            const usernameValidation = security.validateUsername(username);
            if (!usernameValidation.valid) {
                return res.status(200).json({
                    available: false,
                    message: usernameValidation.message
                });
            }

            // Check if username already exists
            const existingUser = await User.findByUsername(username);

            res.status(200).json({
                available: !existingUser,
                message: existingUser ? 'Nome utente già in uso' : 'Nome utente disponibile'
            });
        } catch (error) {
            console.error('Username check error:', error);
            res.status(500).json({
                available: false,
                message: 'Errore del server durante la verifica del nome utente'
            });
        }
    },

    /**
     * Refresh JWT token
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async refreshToken(req, res) {
        try {
            // Get token from cookie or Authorization header
            const token = req.cookies.token ||
                         (req.headers.authorization && req.headers.authorization.split(' ')[1]);

            if (!token) {
                return res.status(401).json({
                    success: false,
                    message: 'Token non presente'
                });
            }

            // Try to decode the token even if expired to get user info
            let decoded;
            try {
                decoded = jwt.verify(token, JWT_SECRET);
            } catch (error) {
                if (error.name === 'TokenExpiredError') {
                    // Token is expired, but we can still decode it to get user info
                    decoded = jwt.decode(token);
                } else {
                    return res.status(401).json({
                        success: false,
                        message: 'Token non valido'
                    });
                }
            }

            if (!decoded || !decoded.id) {
                return res.status(401).json({
                    success: false,
                    message: 'Token non valido'
                });
            }

            // Find user to make sure they still exist and are active
            const user = await User.findById(decoded.id);
            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Utente non trovato'
                });
            }

            if (!user.is_active) {
                return res.status(401).json({
                    success: false,
                    message: 'Account non attivo'
                });
            }

            // Generate new token
            const newToken = jwt.sign(
                {
                    id: user.id,
                    username: user.username
                },
                JWT_SECRET,
                {
                    expiresIn: JWT_EXPIRES_IN
                }
            );

            // Set cookie options
            const cookieOptions = {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict'
            };

            // Set JWT cookie
            res.cookie('token', newToken, cookieOptions);

            res.status(200).json({
                success: true,
                message: 'Token rinnovato con successo',
                token: newToken,
                user: {
                    id: user.id,
                    username: user.username,
                    rank: user.rank
                }
            });
        } catch (error) {
            console.error('Token refresh error:', error);
            res.status(500).json({
                success: false,
                message: 'Errore del server durante il rinnovo del token'
            });
        }
    }
};

module.exports = authController;
