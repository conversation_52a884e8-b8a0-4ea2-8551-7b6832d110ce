// Initialize no-logged user interface

// Mostra automaticamente il popup di login all'apertura della pagina
window.addEventListener('DOMContentLoaded', () => {
    // Controlla se l'utente è già loggato
    const loggedInUser = localStorage.getItem('loggedInUser');
    const token = localStorage.getItem('token');
    
    // Se l'utente è già loggato, reindirizza alla home dei loggati
    if (loggedInUser && token) {
        window.location.href = '/home';
        return;
    }
    
    if (!loggedInUser) {
        // Se non è loggato, mostra il popup di login con animazione fluida
        const loginPopup = document.getElementById('login-popup');
        
        // Aggiungi prima la classe show per display:flex
        setTimeout(() => {
            loginPopup.classList.add('show');
            
            // Aggiungi l'effetto bounce dopo un breve ritardo
            setTimeout(() => {
                loginPopup.classList.add('bounce');
            }, 50);
        }, 100);
        
        // Gestisci la chiusura del popup
        const closeButton = loginPopup.querySelector('.close-modal');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                loginPopup.classList.remove('show', 'bounce');
            });
        }
        
        // Chiudi il popup cliccando fuori
        loginPopup.addEventListener('click', (e) => {
            if (e.target === loginPopup) {
                loginPopup.classList.remove('show', 'bounce');
            }
        });
        // Gestisci il click sul pulsante di login
        const loginButton = document.getElementById('popup-login-button');
        if (loginButton) {
            loginButton.addEventListener('click', async (e) => {
                e.preventDefault();
                
                const username = document.getElementById('popup-login-username').value;
                const password = document.getElementById('popup-login-password').value;
                const rememberMe = document.getElementById('popup-remember-me')?.checked || false;
                const errorMessage = document.getElementById('login-error-message');
                
                if (!username || !password) {
                    if (errorMessage) {
                        errorMessage.textContent = 'Inserisci username e password.';
                    }
                    return;
                }
                
                try {
                    // Disabilita il pulsante durante il login
                    loginButton.disabled = true;
                    loginButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Accesso in corso...';
                    
                    // Esegui il login
                    if (window.authUtils && window.authUtils.login) {
                        await window.authUtils.login(username, password, rememberMe);
                        
                        // Login riuscito, reindirizza alla home dei loggati
                        window.location.href = '/home';
                    }
                } catch (error) {
                    // Login fallito
                    if (errorMessage) {
                        errorMessage.textContent = error.message || 'Login fallito. Verifica username e password.';
                    }
                    
                    // Riabilita il pulsante
                    loginButton.disabled = false;
                    loginButton.innerHTML = '<i class="fas fa-sign-in-alt"></i> Accedi';
                }
            });
        }
    } else {
        // Se è loggato, reindirizza alla home dei loggati
        window.location.href = '/home';
    }
});


// Inizializza gli slot vuoti quando il DOM è caricato
window.addEventListener('DOMContentLoaded', () => {
    // Crea 10 slot vuoti per ogni giocatore
    const player1Hand = document.getElementById('player1-hand');
    const player2Hand = document.getElementById('player2-hand');
    
    // Funzione per creare uno slot vuoto
    function createEmptySlot(index) {
        const slot = document.createElement('div');
        slot.className = 'card-slot empty-slot';
        slot.id = `slot-${index}`;
        return slot;
    }
    
    // Aggiungi 10 slot vuoti per il giocatore 1
    if (player1Hand) {
        player1Hand.innerHTML = ''; // Pulisci prima
        for (let i = 0; i < 10; i++) {
            player1Hand.appendChild(createEmptySlot(`p1-${i}`));
        }
    }
    
    // Aggiungi 10 slot vuoti per il giocatore 2
    if (player2Hand) {
        player2Hand.innerHTML = ''; // Pulisci prima
        for (let i = 0; i < 10; i++) {
            player2Hand.appendChild(createEmptySlot(`p2-${i}`));
        }
    }
});