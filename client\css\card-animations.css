/* Stili e animazioni specifici per la distribuzione delle carte */

/* Prevenzione del glitch delle cover delle carte durante l'animazione */
/* Rimossa animazione stabilizeOpacity perché causava la non visualizzazione delle cover */

/* FIX EMERGENZIALE: Nascondi SOLO lo specifico elemento problematico */
div.card-animation-container.player-white.card-deal-animation[style*="position: absolute"][style*="left: 1211px"][style*="top: 568.763px"] {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    z-index: -9999 !important;
    transform: translateX(-9999px) !important;
}

/* FIX specifico per l'elemento che persiste */
div[style*="position: absolute"][style*="left: 1211px"][style*="top: 568.763px"] {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    z-index: -9999 !important;
}

/* Classe per stabilizzare il mazzo completamente all'inizio del gioco */
.static-deck {
    /* Proprietà necessarie per prevenire refresh continui */
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    will-change: auto !important;
    transition: none !important;
    animation: none !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
    
    /* Contiene tutti i rendering all'interno dell'elemento */
    contain: content !important;
    
    /* Forza la stabilità */
    position: relative;
    z-index: 1;
}

/* Classe per nascondere il deck dopo la distribuzione delle carte */
.hide-deck-after-animation {
    opacity: 0 !important;
    visibility: hidden !important;
    /* Rimossa la trasformazione di scala */
    transition: opacity 0.7s ease, visibility 0.7s ease !important;
    pointer-events: none !important;
}

/* Stabilizzazione dell'area del mazzo durante la distribuzione delle carte */
.dealing-animation-active {
    /* Manteniamo l'elemento visibile ma "congelato" invece di nasconderlo
       Questo previene il flash/refresh causato dalla scomparsa improvvisa */
    opacity: 1 !important;
    visibility: visible !important;
    
    /* Mantiene lo spazio nel layout */
    position: relative;
    z-index: 0;
    display: block !important;
    
    /* Disabilita completamente tutte le animazioni e gli eventi */
    animation: none !important;
    transition: none !important;
    pointer-events: none !important;
    
    /* Rimuovi qualsiasi proprietà che possa causare refresh */
    transform: none !important;
    will-change: auto !important;
    filter: none !important;
    box-shadow: none !important;
}

/* Blocca gli pseudoelementi durante l'animazione */
.dealing-animation-active::before,
.dealing-animation-active::after {
    display: none !important; /* Nascondi completamente piuttosto che usare opacity */
}

/* Blocca tutte le animazioni nei figli dell'elemento */
.dealing-animation-active * {
    animation: none !important;
    transition: none !important;
    transform: none !important;
    contain: paint; /* Ottimizza il rendering */
}

/* Evita il flash delle immagini di cover delle carte */
img.card-back-image {
    opacity: 1; /* Cambiato da 0 a 1 per prevenire la non visualizzazione durante l'animazione */
    /* Rimossi tutti i transform e will-change che causavano refresh continui */
}

/* Versione ottimizzata per l'immagine della cover */
img.card-back-image-optimized {
    opacity: 1;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: transform;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* Stile per le carte animate durante la distribuzione */
.card-deal-animation {
    /* Rimosso will-change che causa refresh continui */
    /* Ridotto al minimo le proprietà che causano repaint */
    background-color: transparent !important;
    opacity: 0; /* Inizia invisibile e viene resa visibile via JavaScript */
    transition: opacity 0.3s ease-out;
}

/* Container ottimizzato per le carte animate (nuova versione) */
.card-animation-container {
    /* Posizionamento e dimensioni base */
    position: absolute;
    width: 130px;
    height: 191px;
    border-radius: 12px;
    background-color: #2a3b4c;
    
    /* Bordo e ombra */
    border: 3px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4), 
                0 0 40px rgba(30, 120, 255, 0.5);
    
    /* Ottimizzazioni per GPU rendering */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 1000px;
    -webkit-perspective: 1000px;
    
    /* Previene flash e refresh durante le manipolazioni DOM */
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
    -webkit-font-smoothing: subpixel-antialiased;
    
    /* Disabilita eventi mouse che potrebbero interferire */
    pointer-events: none;
    
    /* Prevenzione dei refresh quando la carta viene mostrata */
    contain: layout style paint;
    
    /* Transizioni per l'animazione */
    transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.6s ease-out,
                filter 0.6s ease-out,
                box-shadow 0.6s ease-out;
    
    /* Overflow per contenere l'immagine */
    overflow: hidden;
}

/* Assicura che le cover delle carte siano pre-renderizzate */
.preload-card-backs {
    position: absolute;
    width: 1px;
    height: 1px;
    overflow: hidden;
    opacity: 0.01;
    pointer-events: none;
    z-index: -1;
    
    /* Ottimizzazioni per prevenire refresh continui */
    contain: strict; /* Isola completamente questo elemento */
    content-visibility: hidden; /* Nasconde il contenuto fino a quando non è necessario */
    will-change: auto; /* Rimuove qualsiasi ottimizzazione che causa refresh */
    transform: translateZ(0); /* Forza hardware acceleration */
}

/* Stili specifici per i giocatori */
.card-animation-container.player-white {
    background-color: #1a3a6a;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4), 
                0 0 40px rgba(30, 120, 255, 0.5);
}

.card-animation-container.player-black {
    background-color: #3a1a1a;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4), 
                0 0 40px rgba(255, 60, 60, 0.5);
}

/* Animazione di movimento della carta */
@keyframes cardDealAnimation {
    0% {
        transform: scale(0.9) rotate(0deg) translateZ(0);
        opacity: 0;
    }
    20% {
        opacity: 0.95;
    }
    100% {
        transform: scale(1) rotate(0deg) translateZ(0);
        opacity: 1;
    }
}

/* Migliora la visualizzazione delle particelle */
.card-deal-particles {
    position: absolute;
    width: 0;
    height: 0;
    z-index: 150;
    pointer-events: none;
    will-change: transform, opacity;
}

.particle {
    position: absolute;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 10px 5px rgba(100, 150, 255, 0.6);
    transform: translate(-50%, -50%);
    will-change: transform, opacity;
}

/* Immagine della carta retro */
.card-back-image-optimized {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 1;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    border-radius: 8px;
}

/* Overlay di luce sulla carta */
.card-animation-container .card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0) 60%);
    border-radius: 8px;
    z-index: 1;
    pointer-events: none;
    opacity: 0.8;
}

/* FIX: Assicura che i nomi dei giocatori rimangano sempre visibili durante le animazioni */
.player-info-box,
.player-info-box *,
.player-name,
.player-rating,
#player1-area h2,
#player2-area h2,
#player1-area .player-header,
#player2-area .player-header {
    opacity: 1 !important;
    visibility: visible !important;
    display: flex !important;
    z-index: 1000 !important;
    transform: none !important;
    animation: none !important;
    transition: opacity 0s !important;
}

/* Assicura che il contenuto della player-info-box non venga nascosto */
.player-info-box .player-name,
.player-info-box .player-rating {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Previeni che le animazioni delle carte influenzino i player areas */
.dealing-animation-active .player-info-box,
.dealing-animation-active .player-name,
.dealing-animation-active .player-rating {
    opacity: 1 !important;
    visibility: visible !important;
    display: flex !important;
}