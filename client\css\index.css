
/* <PERSON>ili per i pulsanti della hero section */
.hero-section .play-buttons button.primary-btn {
    width: 600px; /* Pulsanti estremamente larghi */
    min-width: 600px;
    max-width: 100%;
    padding: 20px 35px; /* Aumentato anche il padding orizzontale */
    margin: 12px 0;
    height: auto; /* Assicura che l'altezza si adatti al contenuto */
    line-height: 1.6; /* Aumenta lo spazio tra le righe di testo */
    font-size: 1.15em; /* Testo più grande */
    min-height: 70px; /* Altezza minima fissa */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Stile per il testo dei pulsanti */
.hero-section .play-buttons button.primary-btn .primary-text {
    font-size: 1.65em; /* Dimensione font titoli dei pulsanti */
    font-weight: 600;
    display: block;
    margin-bottom: 4px;
}

.hero-section .play-buttons button.primary-btn .secondary-text {
    font-size: 0.9em;
    opacity: 0.9;
}

/* Stile per il pulsante Gioca Online (ex Partita Rapida) */
#quick-game-button {
    background: linear-gradient(135deg, rgba(50, 220, 100, 0.9), rgba(30, 150, 70, 0.9));
    color: white;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 40px rgba(50, 220, 100, 0.4);
    transform: translateY(-1px);
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    border-bottom: 4px solid rgba(0, 50, 0, 0.25);
    transition: all 0.2s ease;
    margin-bottom: 8px;
}

#quick-game-button:hover {
    transform: translateY(-3px);
    box-shadow: 
        0 6px 10px rgba(0, 0, 0, 0.25),
        0 2px 4px rgba(0, 0, 0, 0.15), 
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    border-bottom: 4px solid rgba(0, 50, 0, 0.4);
    background: linear-gradient(135deg, rgba(60, 230, 110, 0.9), rgba(40, 160, 80, 0.9));
}

#quick-game-button:active {
    transform: translateY(2px);
    box-shadow: 
        0 2px 3px rgba(0, 0, 0, 0.15),
        inset 0 1px 2px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(0, 50, 0, 0.25);
}

#quick-game-button .fas {
    color: rgba(255, 255, 255, 1);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
}

/* Fix per abilitare lo scrolling con barra personalizzata */
html {
    height: 100%;
    margin: 0;
    padding: 0;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    overflow-y: scroll !important; /* Forza lo scrolling verticale */
}

body {
    margin: 0;
    padding: 0;
    min-height: 100%;
    position: relative;
    overflow-x: hidden;
}

html::-webkit-scrollbar, body::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}



.main-content {
    height: auto !important;
    overflow: visible !important;
    width: 100% !important;
}

/* Barra di scorrimento personalizzata esterna */
.custom-scrollbar {
    position: fixed;
    top: 0;
    right: 0;
    width: 10px;
    height: 100vh;
    background-color: rgba(10, 18, 34, 0.5);
    z-index: 1000;
    box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
}

.scrollbar-thumb {
    position: absolute;
    top: 0;
    width: 8px;
    right: 1px;
    background-color: #3498db;
    border-radius: 5px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s, width 0.2s, right 0.2s;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

.scrollbar-thumb:hover {
    opacity: 1;
    width: 10px;
    right: 0;
}

/* Stili per la sezione Academy */
.academy-section {
    width: 100%;
    margin: 40px 0 60px 0;
    padding: 40px 30px;
    background-color: rgba(240, 244, 248, 0.8);
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.academy-header {
    text-align: center;
    margin-bottom: 40px;
}

.academy-title {
    text-align: center;
    font-size: 2.8em;
    margin: 0 0 10px 0;
    color: #172a45;
    font-weight: 700;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.academy-subtitle {
    text-align: center;
    font-size: 1.2em;
    color: #5d6d7e;
    margin: 0;
}

.features-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    padding: 0;
}

.feature-card {
    background-color: white;
    border-radius: 12px;
    padding: 25px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 250px;
    border: 1px solid rgba(200, 210, 220, 0.5);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
}

.feature-card i {
    font-size: 2.5em;
    margin-bottom: 15px;
    color: #3498db;
}

.feature-card h3 {
    font-size: 1.4em;
    margin: 0 0 15px 0;
    color: #172a45;
}

.feature-card p {
    margin: 0 0 20px 0;
    color: #5d6d7e;
    line-height: 1.5;
    flex-grow: 1;
}

.feature-card .read-more {
    color: #3498db;
    font-weight: 600;
    margin-top: auto;
    display: inline-block;
    transition: all 0.2s ease;
}

.feature-card:hover .read-more {
    color: #2980b9;
    transform: translateX(5px);
}

/* Responsive per dispositivi più piccoli */
@media (max-width: 768px) {
    .academy-section {
        padding: 30px 15px;
        margin-bottom: 40px;
    }
    
    .features-section {
        grid-template-columns: 1fr;
    }
    
    .academy-title {
        font-size: 2.2em;
    }
    
    .academy-subtitle {
        font-size: 1.1em;
    }
    
    .feature-card {
        min-height: 220px;
    }
}