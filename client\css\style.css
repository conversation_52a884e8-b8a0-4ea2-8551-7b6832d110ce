/* <PERSON><PERSON>ti per tutte le pagine */

/* <PERSON><PERSON> per la pagina di login */
.auth-page {
    background: linear-gradient(135deg, #0a1218, #162736);
    background-image:
        linear-gradient(135deg, #0a1218, #162736),
        url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,0.03)' fill-rule='evenodd'/%3E%3C/svg%3E");
}

.auth-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.4), 0 0 0 1px rgba(255,255,255,0.1);
    overflow: hidden;
    position: relative;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.auth-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 45px rgba(0,0,0,0.5);
}

.auth-logo {
    background: linear-gradient(135deg, #172a45, #1c3d63);
    padding: 25px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.auth-logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #27ae60, #2ecc71, #3498db);
    z-index: 2;
}

.auth-logo::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='rgba(255,255,255,0.05)' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.4;
    z-index: 1;
}

.logo-image {
    position: relative;
    z-index: 3;
    max-width: 220px;
    height: auto;
    filter: drop-shadow(0 5px 15px rgba(0,0,0,0.3));
    transition: all 0.5s ease;
}

.logo-image:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 8px 20px rgba(0,0,0,0.4));
}

.auth-form-container {
    padding: 35px;
    position: relative;
}

.auth-form-container h2 {
    color: #172a45;
    text-align: center;
    font-size: 2rem;
    margin-bottom: 30px;
    font-weight: 700;
    letter-spacing: 0.5px;
    position: relative;
    padding-bottom: 15px;
}

.auth-form-container h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #27ae60, #2ecc71, #3498db);
    border-radius: 3px;
}

.form-group {
    margin-bottom: 22px;
}

.form-group label {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.form-group label i {
    color: #3498db;
}

.form-group input {
    padding: 15px;
    border-radius: 12px;
    border: 2px solid #e0e0e0;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #f9fafc;
    width: 100%;
    box-sizing: border-box;
}

.form-group input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    outline: none;
    background-color: #fff;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.toggle-password {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.toggle-password:hover {
    color: #3498db;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 5px 0 25px;
}

.remember-me input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #3498db;
}

.remember-me label {
    color: #34495e;
    font-size: 0.9rem;
    cursor: pointer;
}

.form-actions {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.primary-btn {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
    padding: 16px;
    border-radius: 12px;
    border: none;
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    position: relative;
    overflow: hidden;
}

.primary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: 0.5s;
}

.primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 20px rgba(39, 174, 96, 0.4);
    background: linear-gradient(135deg, #27ae60 0%, #219653 100%);
}

.primary-btn:hover::before {
    left: 100%;
}

.primary-btn:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(39, 174, 96, 0.3);
}

.auth-links {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    text-align: center;
}

.auth-link {
    color: #3498db;
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
    padding: 5px;
}

.auth-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 1px;
    background: #3498db;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.auth-link:hover {
    color: #2980b9;
}

.auth-link:hover::after {
    width: 80%;
}

.auth-footer {
    padding: 20px 0;
    text-align: center;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
}

.back-to-home {
    color: #3498db;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 20px;
    background-color: rgba(52, 152, 219, 0.1);
}

.back-to-home:hover {
    color: #2980b9;
    transform: translateX(-5px);
    background-color: rgba(52, 152, 219, 0.2);
}

/* Animazione di ingresso */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-container {
    animation: fadeInUp 0.6s ease-out;
}

/* Carte decorative */
.decorative-cards {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
    z-index: -1;
    max-width: 100vw; /* Limita la larghezza massima alla viewport */
}

.card-decoration {
    position: absolute;
    width: 80px;
    height: 120px;
    background-image: url('../img/carte/card-back.webp');
    background-size: cover;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    opacity: 0.15;
    transform: rotate(0deg) translateY(0);
    animation: floatCard 15s infinite ease-in-out;
    max-width: 100vw; /* Limita la larghezza massima alla viewport */
    overflow: hidden; /* Impedisce lo scorrimento orizzontale */
}

.card-1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
    transform: rotate(-15deg);
}

.card-2 {
    top: 70%;
    left: 5%;
    animation-delay: 1s;
    transform: rotate(10deg);
}

.card-3 {
    top: 15%;
    right: 10%;
    animation-delay: 2s;
    transform: rotate(20deg);
}

.card-4 {
    top: 65%;
    right: 8%;
    animation-delay: 3s;
    transform: rotate(-12deg);
}

@keyframes floatCard {
    0% {
        transform: translateY(0) rotate(var(--rotate));
    }
    50% {
        transform: translateY(-20px) rotate(calc(var(--rotate) + 5deg));
    }
    100% {
        transform: translateY(0) rotate(var(--rotate));
    }
}

/* Responsive */
@media (max-width: 600px) {
    .auth-form-container {
        padding: 25px 20px;
    }

    .auth-form-container h2 {
        font-size: 1.6rem;
    }

    .logo-image {
        max-width: 180px;
    }

    .form-group input,
    .primary-btn {
        padding: 12px;
    }

    .decorative-cards {
        display: none;
    }
}

/* Stili per la pagina index.html */
.preload-card-backs {
    position: absolute;
    width: 0;
    height: 0;
    overflow: hidden;
    opacity: 0;
    pointer-events: none;
}

.card-back-image {
    opacity: 0;
}

.card-back-image.card-visible {
    opacity: 1;
}

/* Contenitore per le sezioni della home page */
.index-component {
    align-content: start;
    display: grid;
    flex: 1 0 auto;
    gap: var(--space-24, 24px);
    grid-template-columns: 100%;
    margin: 0 auto;
    max-width: 108.4rem;
    width: 60%; /* Larghezza del 60% su desktop */
    padding: 16px;
    margin-top: 20px;
}

/* Media query per desktop */
@media (min-width: 60em) {
    .index-component {
        padding: var(--space-16, 16px);
        row-gap: 2rem;
        width: 60%; /* Mantiene la larghezza del 60% su desktop */
    }
}

/* Media query per tablet e dispositivi mobili */
@media (max-width: 60em) {
    .index-component {
        width: 80%; /* Aumenta la larghezza all'80% su tablet e mobile */
        padding: 12px;
    }
}

/* Media query per dispositivi mobili più piccoli */
@media (max-width: 30em) {
    .index-component {
        width: 90%; /* Aumenta ulteriormente la larghezza su dispositivi molto piccoli */
        padding: 8px;
        gap: 16px; /* Riduce lo spazio tra gli elementi */
    }
}

#game-container {
    background: linear-gradient(135deg, #172a45 0%, #0f1b2d 50%, #0a1222 100%);
    will-change: transform, opacity;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    backface-visibility: hidden;
}

#board-area {
    will-change: transform, opacity;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

#game-board {
    will-change: transform, opacity;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    height: 100%;
}

#advantage-indicator {
    width: 25px;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0,0,0,0.3), inset 0 0 8px rgba(0,0,0,0.3);
    margin: 0;
    padding: 0;
}

.advantage-white {
    width: 100%;
    height: 50%;
    position: absolute;
    top: 0;
    border-radius: 0;
}

.advantage-black {
    width: 100%;
    height: 50%;
    position: absolute;
    bottom: 0;
    border-radius: 0;
}

#skemino-notation-area {
    transform: scale(1) !important;
    zoom: 1 !important;
}

#deck {
    display: block;
    width: 90%; /* Ridotto per creare un effetto compresso */
    height: 90%; /* Ridotto per creare un effetto compresso */
    position: relative;
    /* Evita trasformazioni che potrebbero causare refresh */
    transform: none !important;

    /* Riquadro interno senza effetti di illuminazione */
    border: 3px solid rgba(255, 255, 255, 0.4);
    border-radius: 15px;
    background-color: #1c3854;
    box-shadow: none;

    /* Disabilita interazione e animazioni */
    pointer-events: none !important;
    transition: none !important;
    animation: none !important;
    will-change: auto !important;

    /* Centra nel contenitore parent */
    margin: auto;
}

/* Disabilita qualsiasi effetto hover sul mazzo */
#deck:hover,
.deck-area:hover {
    transform: none !important;
    box-shadow:
        0 0 10px rgba(0, 40, 80, 0.4),
        inset 0 0 5px rgba(255, 255, 255, 0.1) !important;
    filter: none !important;
    opacity: 1 !important;
    border-color: rgba(255, 255, 255, 0.6) !important;
}

/* Contatore del mazzo rimosso */

.hero-title-container {
    margin-top: 60px;
}

#board-sidebar {
    height: 100%;
}

.deck-area {
    position: fixed; /* Fixed per centrare rispetto alla finestra del browser */
    width: 180px;
    height: 260px; /* Aumentata altezza per estendere la parte inferiore */
    left: 50%; /* Centro orizzontale */
    top: 50%; /* Centro verticale */
    transform: translate(-50%, -48%); /* Spostato leggermente verso l'alto per estendere la parte inferiore */
    z-index: 1000; /* Valore alto per essere sopra tutti gli altri elementi */
    text-align: center;

    /* Riquadro esterno con illuminazione ridotta */
    padding: 12px 12px 32px 12px; /* Padding inferiore aumentato */
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(0, 65, 130, 0.4), rgba(0, 20, 40, 0.25));
    box-shadow:
        0 0 20px 5px rgba(0, 40, 80, 0.2),
        inset 0 0 10px rgba(0, 70, 140, 0.15);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    border: 2px solid rgba(255, 255, 255, 0.2);

    /* Effetto riquadro compresso/rientrante */
    box-sizing: border-box;
    overflow: visible;

    /* Disabilitiamo completamente le transizioni e animazioni */
    pointer-events: none !important;
    transition: none !important;
    animation: none !important;

    /* Inizialmente nascosto, apparirà dopo l'animazione dei dadi */
    visibility: hidden;
    opacity: 0;
}

/* Etichetta del mazzo statica */
.deck-label {
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
    pointer-events: none;
    text-transform: uppercase;
    letter-spacing: 1px;
    /* Rimuovi qualsiasi effetto che potrebbe causare refresh */
    text-shadow: none;
    transition: none !important;
    animation: none !important;
}