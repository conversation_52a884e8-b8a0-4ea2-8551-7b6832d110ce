/**
 * animation-cleanup.js
 * 
 * Sistema unificato di pulizia e gestione delle animazioni delle carte.
 * Si occupa della rimozione degli elementi di animazione residui e della 
 * gestione della visibilità del mazzo.
 * 
 * DETTAGLI DEL FUNZIONAMENTO:
 * 
 * 1. SCOPO:
 *    - Rileva e rimuove gli elementi di animazione residui
 *    - Gestisce correttamente la visibilità del mazzo di carte
 *    - Rimuove specificamente l'elemento problematico con coordinate precise
 *    - Garantisce un'esperienza pulita anche dopo iconizzazione del browser
 * 
 * 2. STRUTTURA:
 *    - MutationObserver per rilevare e rimuovere elementi problematici
 *    - Stili CSS di emergenza iniettati dinamicamente
 *    - Gestione automatica della visibilità del mazzo
 *    - Eventi per rilevare cambio visibilità e focus
 */

(function() {
    // Configurazione unificata
    const config = {
        logEvents: true,
        cleanupDelay: 100,
        autocleanInterval: 5000,
        
        // Selettore specifico per l'elemento problematico
        problemSelector: 'div.card-animation-container.player-white.card-deal-animation[style*="position: absolute"][style*="left: 1211px"][style*="top: 568.763px"], div[style*="position: absolute"][style*="left: 1211px"][style*="top: 568.763px"]',
        
        // Selettori generici per le animazioni
        selectors: {
            // Elementi di animazione da rimuovere dopo la distribuzione iniziale
            animationContainers: [
                '.card-animation-container',
                '.card-deal-animation',
                '.animating-card',
                '.animation-pending'
            ],
            // Container in cui gli elementi di animazione possono essere nidificati
            parentContainers: [
                '#game-container',
                '.deck-area',
                '#board-area',
                '#game-board'
            ]
        }
    };

    // Stato unificato
    const state = {
        lastCleanup: 0,          // Timestamp dell'ultima pulizia
        cleanupCount: 0,         // Numero di pulizie effettuate
        removedElements: 0,      // Elementi rimossi in totale
        autocleanTimer: null,    // Timer per la pulizia automatica
        observer: null,          // MutationObserver
        deckVisible: true,       // Stato visibilità del mazzo
        gameStarted: false,      // Se il gioco è iniziato
        initialAnimationDone: false, // Se l'animazione iniziale è completata
        initialAnimationTimeout: null // Timer per considerare completata l'animazione iniziale
    };

    /**
     * Inizializza il sistema unificato
     */
    function initialize() {
        if (config.logEvents) {
            console.log('[ANIMATION SYSTEM] Inizializzazione del sistema unificato di animazioni');
        }
        
        // Aggiungi stili CSS di emergenza
        injectEmergencyStyles();
        
        // Configura mutation observer per rimuovere elementi problematici
        setupMutationObserver();
        
        // Imposta timer per la pulizia automatica
        if (state.autocleanTimer) {
            clearInterval(state.autocleanTimer);
        }
        state.autocleanTimer = setInterval(performAutomaticCleanup, config.autocleanInterval);
        
        // Registra eventi per rilevare cambio visibilità/focus
        registerEventListeners();
        
        // Esegui una pulizia iniziale
        setTimeout(() => {
            // Rimuovi solo elementi problematici specifici all'inizio
            cleanupProblemElements();
        }, 1000);
        
        // Imposta timer per considerare completata l'animazione iniziale
        state.initialAnimationTimeout = setTimeout(() => {
            state.initialAnimationDone = true;
            if (config.logEvents) {
                console.log('[ANIMATION SYSTEM] Animazione iniziale considerata completata');
            }
            
            // Ora possiamo fare una pulizia completa
            performCleanup();
        }, 7000); // 7 secondi dovrebbero essere sufficienti per l'animazione iniziale
        
        if (config.logEvents) {
            console.log('[ANIMATION SYSTEM] Sistema unificato inizializzato');
        }
    }

    /**
     * Inietta stili CSS di emergenza nel DOM
     */
    function injectEmergencyStyles() {
        const styleElement = document.createElement('style');
        styleElement.type = 'text/css';
        styleElement.id = 'animation-cleanup-styles';
        styleElement.innerHTML = `
            /* FIX SPECIFICO: Nascondi solo l'elemento problematico */
            div.card-animation-container.player-white.card-deal-animation[style*="position: absolute"][style*="left: 1211px"][style*="top: 568.763px"],
            div[style*="position: absolute"][style*="left: 1211px"][style*="top: 568.763px"] {
                display: none !important;
                opacity: 0 !important;
                visibility: hidden !important;
                z-index: -9999 !important;
                transform: translateX(-9999px) !important;
                position: absolute !important;
                pointer-events: none !important;
            }
            
            /* Stili di pulizia post-animazione (applicati dopo l'animazione iniziale) */
            .cleanup-applied .card-animation-container:not(.initial-animation),
            .cleanup-applied .card-deal-animation:not(.initial-animation) {
                display: none !important;
                opacity: 0 !important;
                visibility: hidden !important;
            }
        `;
        document.head.appendChild(styleElement);
        
        if (config.logEvents) {
            console.log('[ANIMATION SYSTEM] Stili di emergenza iniettati nel DOM');
        }
    }

    /**
     * Configura il MutationObserver
     */
    function setupMutationObserver() {
        // Rimuovi observer esistente se presente
        if (state.observer) {
            state.observer.disconnect();
            state.observer = null;
        }
        
        // Crea nuovo observer
        state.observer = new MutationObserver(function(mutations) {
            let problematicNodeFound = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    Array.from(mutation.addedNodes).forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            // Durante l'animazione iniziale, rimuovi solo elementi problematici specifici
                            if (!state.initialAnimationDone) {
                                // Verifica se è l'elemento problematico specifico
                                if (isProblemElement(node)) {
                                    node.remove();
                                    problematicNodeFound = true;
                                    if (config.logEvents) {
                                        console.log('[ANIMATION SYSTEM] Rimosso elemento problematico specifico');
                                    }
                                }
                            } else {
                                // Dopo l'animazione iniziale, rimuovi tutti gli elementi di animazione
                                if (isAnimationElement(node)) {
                                    // Verifica se non è un'animazione attualmente necessaria
                                    if (!node.classList.contains('initial-animation')) {
                                        node.remove();
                                        problematicNodeFound = true;
                                    }
                                }
                            }
                            
                            // Cerca anche all'interno del nodo
                            if (node.querySelectorAll) {
                                // Durante l'animazione iniziale, cerca solo elementi problematici
                                const selector = !state.initialAnimationDone 
                                    ? config.problemSelector 
                                    : config.selectors.animationContainers.join(', ');
                                
                                const problematicChildren = node.querySelectorAll(selector);
                                if (problematicChildren.length > 0) {
                                    problematicChildren.forEach(function(child) {
                                        // Rimuovi solo se è un elemento problematico o se l'animazione iniziale è completata
                                        if (isProblemElement(child) || state.initialAnimationDone) {
                                            child.remove();
                                            problematicNodeFound = true;
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
            });
            
            if (problematicNodeFound) {
                state.cleanupCount++;
                if (config.logEvents) {
                    console.log(`[ANIMATION SYSTEM] Ripuliti elementi problematici - #${state.cleanupCount}`);
                }
            }
        });
        
        // Inizia ad osservare tutto il DOM
        state.observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        if (config.logEvents) {
            console.log('[ANIMATION SYSTEM] Observer configurato');
        }
    }

    /**
     * Verifica se un elemento è l'elemento problematico specifico
     * @param {Element} node - Elemento DOM da verificare
     * @returns {boolean} - true se è un elemento problematico
     */
    function isProblemElement(node) {
        // Verifica classi
        if (node.classList && 
            node.classList.contains('card-animation-container') && 
            node.classList.contains('player-white') &&
            node.classList.contains('card-deal-animation')) {
            
            // Verifica coordinate
            if (node.style && 
                node.style.left && node.style.left.includes('1211') && 
                node.style.top && node.style.top.includes('568')) {
                return true;
            }
        }
        
        // Verifica stile inline
        if (node.getAttribute && node.getAttribute('style') && 
            node.getAttribute('style').includes('position: absolute') &&
            node.getAttribute('style').includes('left: 1211px') &&
            node.getAttribute('style').includes('top: 568.763px')) {
            return true;
        }
        
        return false;
    }

    /**
     * Verifica se un elemento è un elemento di animazione
     * @param {Element} node - Elemento DOM da verificare
     * @returns {boolean} - true se è un elemento di animazione
     */
    function isAnimationElement(node) {
        if (node.classList) {
            for (const className of config.selectors.animationContainers) {
                // Rimuovi il . iniziale dalla classe
                const cls = className.replace(/^\./, '');
                if (node.classList.contains(cls)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Registra eventi per rilevare cambio visibilità/focus
     */
    function registerEventListeners() {
        // Evento visibilitychange
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                if (config.logEvents) {
                    console.log('[ANIMATION SYSTEM] Pagina tornata visibile, eseguo pulizia animazioni');
                }
                
                // Quando la pagina torna visibile, esegui pulizia
                cleanupProblemElements(); // Pulisci immediatamente gli elementi problematici
                
                // Pulisci anche dopo un ritardo per essere sicuri
                setTimeout(state.initialAnimationDone ? performCleanup : cleanupProblemElements, config.cleanupDelay);
                setTimeout(state.initialAnimationDone ? performCleanup : cleanupProblemElements, config.cleanupDelay * 5);
            }
        });
        
        // Evento focus
        window.addEventListener('focus', function() {
            if (config.logEvents) {
                console.log('[ANIMATION SYSTEM] Finestra ha ricevuto il focus, eseguo pulizia animazioni');
            }
            
            // Quando la finestra riceve il focus, esegui pulizia
            cleanupProblemElements(); // Pulisci immediatamente gli elementi problematici
            
            // Pulisci anche dopo un ritardo per essere sicuri
            setTimeout(state.initialAnimationDone ? performCleanup : cleanupProblemElements, config.cleanupDelay);
        });
        
        // Evento blur
        window.addEventListener('blur', function() {
            if (config.logEvents) {
                console.log('[ANIMATION SYSTEM] Finestra ha perso il focus');
            }
        });
        
        // Aggiungi listener per eventi del socket (per partite multiplayer)
        if (window.socket) {
            window.socket.on('gameStateUpdate', function() {
                if (config.logEvents) {
                    console.log('[ANIMATION SYSTEM] Ricevuto aggiornamento stato gioco');
                }
                
                // Se ricevi un aggiornamento dello stato di gioco, considera completata l'animazione iniziale
                if (!state.initialAnimationDone) {
                    state.initialAnimationDone = true;
                    if (config.logEvents) {
                        console.log('[ANIMATION SYSTEM] Animazione iniziale completata da evento di gioco');
                    }
                }
                
                // Pulisci con un breve ritardo
                setTimeout(performCleanup, config.cleanupDelay * 2);
            });
        }
    }

    /**
     * Esegue la pulizia automatica periodica
     */
    function performAutomaticCleanup() {
        // Esegui la pulizia solo se:
        // 1. La pagina è visibile
        // 2. È passato abbastanza tempo dall'ultima pulizia
        // 3. Siamo in una partita attiva o l'animazione iniziale è completata
        if (document.visibilityState === 'visible' && 
            Date.now() - state.lastCleanup > config.autocleanInterval &&
            (window.currentGameState || window.onlineGameManager?.gameId || state.initialAnimationDone)) {
            
            // Se l'animazione iniziale è completata, esegui pulizia completa
            if (state.initialAnimationDone) {
                performCleanup(true);
            } else {
                // Altrimenti pulisci solo gli elementi problematici
                cleanupProblemElements();
            }
        }
    }

    /**
     * Pulisce specificamente gli elementi problematici
     */
    function cleanupProblemElements() {
        const problematicElements = document.querySelectorAll(config.problemSelector);
        let count = 0;
        
        if (problematicElements.length > 0) {
            problematicElements.forEach(function(element) {
                element.remove();
                count++;
            });
            
            if (config.logEvents && count > 0) {
                console.log(`[ANIMATION SYSTEM] Rimossi ${count} elementi problematici specifici`);
            }
        }
        
        state.lastCleanup = Date.now();
        state.removedElements += count;
        
        return count;
    }

    /**
     * Esegue la pulizia completa degli elementi di animazione
     * @param {boolean} isAutomatic - Se la pulizia è automatica o manuale
     */
    function performCleanup(isAutomatic = false) {
        // Se l'animazione iniziale non è ancora completata, pulisci solo elementi problematici
        if (!state.initialAnimationDone) {
            return cleanupProblemElements();
        }
        
        state.lastCleanup = Date.now();
        state.cleanupCount++;
        let removedInThisRun = 0;
        
        // 1. Rimuovi elementi problematici specifici
        removedInThisRun += cleanupProblemElements();
        
        // 2. Pulizia degli elementi di animazione diretta
        config.selectors.animationContainers.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                elements.forEach(element => {
                    // Non rimuovere elementi di animazione iniziale se il gioco è appena iniziato
                    if (!element.classList.contains('initial-animation')) {
                        element.remove();
                        removedInThisRun++;
                    }
                });
            }
        });
        
        // 3. Cerca anche all'interno dei container genitori (per elementi annidati)
        config.selectors.parentContainers.forEach(containerSelector => {
            const container = document.querySelector(containerSelector);
            if (container) {
                // Cerca elementi di animazione all'interno del container
                config.selectors.animationContainers.forEach(selector => {
                    const nestedElements = container.querySelectorAll(selector);
                    nestedElements.forEach(element => {
                        // Non rimuovere elementi di animazione iniziale se il gioco è appena iniziato
                        if (!element.classList.contains('initial-animation')) {
                            element.remove();
                            removedInThisRun++;
                        }
                    });
                });
            }
        });
        
        state.removedElements += removedInThisRun;
        
        if (removedInThisRun > 0 && config.logEvents) {
            console.log(`[ANIMATION SYSTEM] Rimossi ${removedInThisRun} elementi di animazione residui. Totale: ${state.removedElements}`);
        } else if (!isAutomatic && config.logEvents) {
            console.log('[ANIMATION SYSTEM] Nessun elemento di animazione residuo trovato');
        }
        
        // Assicurati che il mazzo sia correttamente visualizzato/nascosto
        cleanupDeckArea();
        
        return removedInThisRun;
    }

    /**
     * Pulisce e corregge lo stato dell'area del mazzo
     */
    function cleanupDeckArea() {
        // Usa il deck-visibility-fix se disponibile
        if (window.deckVisibilityFix) {
            window.deckVisibilityFix.cleanup();
            
            // Se siamo in una partita, potrebbe essere necessario nascondere il mazzo
            if (window.currentGameState && window.currentGameState.gameStarted) {
                window.deckVisibilityFix.hideDeck();
            }
        } else {
            // Fallback se il modulo specifico non è disponibile
            const deckElement = document.getElementById('deck');
            const deckArea = document.querySelector('.deck-area');
            const cardStack = document.querySelector('.card-stack-visual');
            
            if (deckElement && window.currentGameState && window.currentGameState.gameStarted) {
                // Nascondi il mazzo se il gioco è iniziato
                deckElement.classList.add('hide-deck-after-animation');
                deckElement.style.opacity = '0';
                deckElement.style.visibility = 'hidden';
                
                // Nascondi anche il card stack se presente
                if (cardStack) {
                    cardStack.style.opacity = '0';
                    cardStack.style.visibility = 'hidden';
                }
            }
            
            // Rimuovi eventuali classi che potrebbero causare problemi
            if (deckArea) {
                deckArea.classList.remove('dealing-animation-active');
            }
        }
    }

    /**
     * Imposta lo stato di animazione iniziale come completato
     */
    function setInitialAnimationDone() {
        state.initialAnimationDone = true;
        
        // Aggiungi classe al body per applicare stili CSS di pulizia
        document.body.classList.add('cleanup-applied');
        
        // Esegui una pulizia completa
        performCleanup();
        
        if (config.logEvents) {
            console.log('[ANIMATION SYSTEM] Animazione iniziale marcata come completata');
        }
    }

    /**
     * Registra che il gioco è iniziato
     */
    function setGameStarted() {
        state.gameStarted = true;
        
        // Imposta un timeout per considerare completata l'animazione iniziale
        // se non è già stata completata
        if (!state.initialAnimationDone) {
            setTimeout(() => {
                setInitialAnimationDone();
                
                // Dopo 2 secondi, nascondi il mazzo
                setTimeout(() => {
                    hideDeck();
                }, 2000);
            }, 5000); // 5 secondi dovrebbero essere sufficienti per l'animazione iniziale
        } else {
            // Se l'animazione è già completata, nascondi il mazzo
            setTimeout(() => {
                hideDeck();
            }, 2000);
        }
        
        if (config.logEvents) {
            console.log('[ANIMATION SYSTEM] Gioco marcato come iniziato');
        }
    }

    /**
     * Nasconde il mazzo di carte
     */
    function hideDeck() {
        const deckElement = document.getElementById('deck');
        if (deckElement) {
            deckElement.classList.add('hide-deck-after-animation');
            deckElement.style.opacity = '0';
            deckElement.style.visibility = 'hidden';
            
            state.deckVisible = false;
            
            if (config.logEvents) {
                console.log('[ANIMATION SYSTEM] Mazzo nascosto');
            }
        }
    }

    /**
     * Mostra il mazzo di carte
     */
    function showDeck() {
        const deckElement = document.getElementById('deck');
        if (deckElement) {
            deckElement.classList.remove('hide-deck-after-animation');
            deckElement.style.opacity = '1';
            deckElement.style.visibility = 'visible';
            
            state.deckVisible = true;
            
            if (config.logEvents) {
                console.log('[ANIMATION SYSTEM] Mazzo mostrato');
            }
        }
    }

    /**
     * Esegue pulizia emergenziale
     */
    function emergencyCleanup() {
        setInitialAnimationDone(); // Forza completamento animazione iniziale
        
        // Pulisci elementi problematici
        cleanupProblemElements();
        
        // Esegui pulizia completa
        performCleanup();
        
        // Aggiungi classe di pulizia al body
        document.body.classList.add('cleanup-applied');
        
        if (config.logEvents) {
            console.log('[ANIMATION SYSTEM] Eseguita pulizia emergenziale');
        }
    }

    /**
     * Esegue una pulizia delle animazioni e degli elementi residui
     */
    function cleanup() {
        if (state.initialAnimationDone) {
            performCleanup();
        } else {
            cleanupProblemElements();
        }
    }

    // Esponi API pubblica unificata
    window.animationSystem = {
        cleanup,
        hideDeck,
        showDeck,
        setGameStarted,
        setInitialAnimationDone,
        emergencyCleanup,
        getState: () => ({ ...state })
    };

    // Per retrocompatibilità, esponi anche le vecchie API
    window.animationCleanup = {
        cleanup,
        initialize: () => {}, // Funzione vuota per retrocompatibilità
        getState: () => ({ ...state })
    };
    
    window.deckVisibilityFix = {
        hideDeck,
        showDeck,
        cleanup,
        setGameStarted,
        getState: () => ({ ...state })
    };

    // Inizializza automaticamente
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // Aggiungi scorciatoia da tastiera per pulizia emergenziale
    document.addEventListener('keydown', function(e) {
        // Combinazione Alt+Shift+C per eseguire la pulizia di emergenza
        if (e.altKey && e.shiftKey && e.key === 'C') {
            emergencyCleanup();
        }
    });
})();