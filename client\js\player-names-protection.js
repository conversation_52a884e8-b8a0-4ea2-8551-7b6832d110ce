/**
 * player-names-protection.js
 * 
 * Protegge i nomi dei giocatori durante le animazioni delle carte
 * Previene la scomparsa temporanea dei nomi nei player-info-box
 */

(function() {
    'use strict';
    
    // Memorizza i nomi dei giocatori
    let savedPlayer1Name = '';
    let savedPlayer2Name = '';
    let protectionActive = false;
    
    // Funzione per salvare i nomi correnti
    function savePlayerNames() {
        const player1NameElement = document.querySelector('#player1-area .player-name');
        const player2NameElement = document.querySelector('#player2-area .player-name');
        
        if (player1NameElement && player1NameElement.textContent && 
            player1NameElement.textContent !== '...' && 
            player1NameElement.textContent !== '') {
            savedPlayer1Name = player1NameElement.textContent;
            console.log('[NAMES PROTECTION] Salvato nome player 1:', savedPlayer1Name);
        }
        
        if (player2NameElement && player2NameElement.textContent && 
            player2NameElement.textContent !== '...' && 
            player2NameElement.textContent !== '') {
            savedPlayer2Name = player2NameElement.textContent;
            console.log('[NAMES PROTECTION] Salvato nome player 2:', savedPlayer2Name);
        }
    }
    
    // Funzione per ripristinare i nomi se vengono svuotati
    function protectPlayerNames() {
        if (!protectionActive) return;
        
        const player1NameElement = document.querySelector('#player1-area .player-name');
        const player2NameElement = document.querySelector('#player2-area .player-name');
        
        // Ripristina nome player 1 se vuoto
        if (player1NameElement && savedPlayer1Name && 
            (!player1NameElement.textContent || player1NameElement.textContent === '' || 
             player1NameElement.textContent === '...')) {
            player1NameElement.textContent = savedPlayer1Name;
            console.log('[NAMES PROTECTION] Ripristinato nome player 1:', savedPlayer1Name);
        }
        
        // Ripristina nome player 2 se vuoto
        if (player2NameElement && savedPlayer2Name && 
            (!player2NameElement.textContent || player2NameElement.textContent === '' || 
             player2NameElement.textContent === '...')) {
            player2NameElement.textContent = savedPlayer2Name;
            console.log('[NAMES PROTECTION] Ripristinato nome player 2:', savedPlayer2Name);
        }
    }
    
    // Observer per monitorare le modifiche ai nomi
    function setupNameObserver() {
        const config = { 
            childList: true, 
            subtree: true, 
            characterData: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        };
        
        // Observer per player 1
        const player1Area = document.getElementById('player1-area');
        if (player1Area) {
            const observer1 = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'characterData' || mutation.type === 'childList') {
                        const nameElement = player1Area.querySelector('.player-name');
                        if (nameElement && savedPlayer1Name) {
                            // Se il nome viene svuotato durante l'animazione, ripristinalo
                            if (!nameElement.textContent || nameElement.textContent === '' || 
                                nameElement.textContent === '...') {
                                requestAnimationFrame(() => {
                                    if (protectionActive) {
                                        nameElement.textContent = savedPlayer1Name;
                                        console.log('[NAMES PROTECTION] Ripristinato nome player 1 tramite observer');
                                    }
                                });
                            }
                        }
                    }
                });
            });
            observer1.observe(player1Area, config);
        }
        
        // Observer per player 2
        const player2Area = document.getElementById('player2-area');
        if (player2Area) {
            const observer2 = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'characterData' || mutation.type === 'childList') {
                        const nameElement = player2Area.querySelector('.player-name');
                        if (nameElement && savedPlayer2Name) {
                            // Se il nome viene svuotato durante l'animazione, ripristinalo
                            if (!nameElement.textContent || nameElement.textContent === '' || 
                                nameElement.textContent === '...') {
                                requestAnimationFrame(() => {
                                    if (protectionActive) {
                                        nameElement.textContent = savedPlayer2Name;
                                        console.log('[NAMES PROTECTION] Ripristinato nome player 2 tramite observer');
                                    }
                                });
                            }
                        }
                    }
                });
            });
            observer2.observe(player2Area, config);
        }
    }
    
    // Attiva la protezione durante le animazioni delle carte
    function activateProtection() {
        console.log('[NAMES PROTECTION] Protezione attivata');
        protectionActive = true;
        savePlayerNames();
        
        // Controlla periodicamente durante l'animazione
        const protectionInterval = setInterval(protectPlayerNames, 50);
        
        // Disattiva dopo 30 secondi (tempo più che sufficiente per tutte le animazioni)
        setTimeout(() => {
            clearInterval(protectionInterval);
            // Non disattivare la protezione se i nomi sono ancora vuoti
            const player1Name = document.querySelector('#player1-area .player-name');
            const player2Name = document.querySelector('#player2-area .player-name');
            
            if (player1Name && player2Name && 
                player1Name.textContent && player1Name.textContent !== '...' &&
                player2Name.textContent && player2Name.textContent !== '...') {
                protectionActive = false;
                console.log('[NAMES PROTECTION] Protezione disattivata - nomi presenti');
            } else {
                console.log('[NAMES PROTECTION] Protezione mantenuta attiva - nomi ancora vuoti');
                // Riattiva la protezione per altri 10 secondi
                setTimeout(() => activateProtection(), 100);
            }
        }, 30000);
    }
    
    // Intercetta l'inizio delle animazioni delle carte
    const originalAnimateFunction = window.animateDealCard || function() {};
    window.animateDealCard = function(...args) {
        console.log('[NAMES PROTECTION] animateDealCard intercettato');
        activateProtection();
        return originalAnimateFunction.apply(this, args);
    };
    
    // Intercetta anche le funzioni che potrebbero svuotare i nomi
    if (window.socket) {
        const originalOn = window.socket.on;
        window.socket.on = function(event, callback) {
            if (event === 'gameState' || event === 'gameStateUpdate') {
                const wrappedCallback = function(...args) {
                    console.log('[NAMES PROTECTION] Socket event intercettato:', event);
                    // Salva i nomi prima che vengano modificati
                    savePlayerNames();
                    const result = callback.apply(this, args);
                    // Attiva protezione dopo l'evento
                    setTimeout(() => {
                        activateProtection();
                    }, 100);
                    return result;
                };
                return originalOn.call(this, event, wrappedCallback);
            }
            return originalOn.call(this, event, callback);
        };
    }
    
    // Monitora anche altri eventi che potrebbero triggerare animazioni
    document.addEventListener('gameStateUpdate', () => {
        console.log('[NAMES PROTECTION] Event gameStateUpdate ricevuto');
        activateProtection();
    });
    document.addEventListener('startCardAnimation', () => {
        console.log('[NAMES PROTECTION] Event startCardAnimation ricevuto');
        activateProtection();
    });
    
    // Attiva protezione quando viene rilevata la classe dealing-animation-active
    const bodyObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                const target = mutation.target;
                if (target.classList && target.classList.contains('dealing-animation-active')) {
                    activateProtection();
                }
            }
        });
    });
    
    // Osserva il body e i suoi figli per la classe dealing-animation-active
    bodyObserver.observe(document.body, {
        attributes: true,
        attributeFilter: ['class'],
        subtree: true
    });
    
    // Intercetta specificamente il momento in cui i nomi potrebbero essere svuotati
    function interceptTextContentChanges() {
        const player1NameElement = document.querySelector('#player1-area .player-name');
        const player2NameElement = document.querySelector('#player2-area .player-name');
        
        if (player1NameElement) {
            const originalSetter1 = Object.getOwnPropertyDescriptor(Node.prototype, 'textContent').set;
            Object.defineProperty(player1NameElement, 'textContent', {
                set: function(value) {
                    console.log('[NAMES PROTECTION] Tentativo di modificare player1 textContent:', value);
                    if ((!value || value === '' || value === '...') && savedPlayer1Name && protectionActive) {
                        console.log('[NAMES PROTECTION] BLOCCATO svuotamento player1, mantengo:', savedPlayer1Name);
                        originalSetter1.call(this, savedPlayer1Name);
                    } else {
                        originalSetter1.call(this, value);
                        if (value && value !== '...' && value !== '') {
                            savedPlayer1Name = value;
                        }
                    }
                },
                get: function() {
                    return this.innerText;
                }
            });
        }
        
        if (player2NameElement) {
            const originalSetter2 = Object.getOwnPropertyDescriptor(Node.prototype, 'textContent').set;
            Object.defineProperty(player2NameElement, 'textContent', {
                set: function(value) {
                    console.log('[NAMES PROTECTION] Tentativo di modificare player2 textContent:', value);
                    if ((!value || value === '' || value === '...') && savedPlayer2Name && protectionActive) {
                        console.log('[NAMES PROTECTION] BLOCCATO svuotamento player2, mantengo:', savedPlayer2Name);
                        originalSetter2.call(this, savedPlayer2Name);
                    } else {
                        originalSetter2.call(this, value);
                        if (value && value !== '...' && value !== '') {
                            savedPlayer2Name = value;
                        }
                    }
                },
                get: function() {
                    return this.innerText;
                }
            });
        }
    }
    
    // Inizializza quando il DOM è pronto
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setupNameObserver();
            interceptTextContentChanges();
            // Salva i nomi iniziali se presenti
            setTimeout(() => {
                savePlayerNames();
                // Attiva protezione preventiva durante le animazioni iniziali
                activateProtection();
            }, 1000);
        });
    } else {
        setupNameObserver();
        interceptTextContentChanges();
        // Salva i nomi iniziali se presenti
        setTimeout(() => {
            savePlayerNames();
            // Attiva protezione preventiva durante le animazioni iniziali
            activateProtection();
        }, 1000);
    }
    
    // Intercetta anche renderHand per prevenire svuotamento durante animazioni
    setTimeout(() => {
        const originalRenderHand = window.renderHand;
        if (originalRenderHand) {
            window.renderHand = function(handElement, cards, isClickable) {
                console.log('[NAMES PROTECTION] renderHand intercettato, cards:', cards?.length);
                
                // Se stiamo per renderizzare 0 carte durante un'animazione, attiva protezione
                if (!cards || cards.length === 0) {
                    console.log('[NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata');
                    savePlayerNames();
                    activateProtection();
                    
                    // Applica protezione immediata
                    setTimeout(() => {
                        protectPlayerNames();
                    }, 10);
                }
                
                return originalRenderHand.call(this, handElement, cards, isClickable);
            };
        }
    }, 50);
    
    // Intercetta anche la funzione specifica di animazione carte
    setTimeout(() => {
        const originalAnimateCardDealing = window.animateCardDealing;
        if (originalAnimateCardDealing) {
            window.animateCardDealing = function(...args) {
                console.log('[NAMES PROTECTION] animateCardDealing intercettato');
                savePlayerNames();
                activateProtection();
                return originalAnimateCardDealing.apply(this, args);
            };
        }
    }, 100);
    
    // Esponi API pubblica
    window.playerNamesProtection = {
        saveNames: savePlayerNames,
        protect: protectPlayerNames,
        activate: activateProtection,
        interceptTextContent: interceptTextContentChanges
    };
    
    console.log('[NAMES PROTECTION] Sistema di protezione nomi caricato');
})();