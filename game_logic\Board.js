const { Card, SUITS } = require('./Card');

const ROWS = 6;
const COLS = 6; // a-f

class Board {
    constructor() {
        // Griglia: mappa da posizione (es. "c4") a oggetto Card o null
        this.grid = this.initializeGrid();
        // Controllo Vertici: mappa da id vertice (es. "vertex-a1") a colore giocatore ("white", "black") o null
        this.vertexControl = {
            "vertex-a1": null, // Top-Left (White)
            "vertex-f1": null, // Top-Right (White)
            "vertex-a6": null, // Bottom-Left (Black)
            "vertex-f6": null, // Bottom-Right (Black)
        };
        this.initialCardPlaced = false; // Flag per la prima carta piazzata
        this.setupVertices = new Set(); // Set per tenere traccia dei vertici che hanno ricevuto una carta di setup iniziale
    }

    initializeGrid() {
        const grid = {};
        for (let r = 1; r <= ROWS; r++) {
            for (let c = 1; c <= COLS; c++) {
                const colLetter = String.fromCharCode(96 + c);
                grid[`${colLetter}${r}`] = null; // Inizialmente vuoto
            }
        }
        return grid;
    }

    /**
     * Ottiene la carta in una specifica posizione.
     * @param {string} position Es. "c4"
     * @returns {Card | null}
     */
    getCardAt(position) {
        return this.grid[position] || null;
    }

     /**
     * Controlla se una posizione è valida sulla griglia.
     * @param {string} position Es. "c4"
     * @returns {boolean}
     */
    isValidPosition(position) {
        return position in this.grid;
    }

    /**
     * Controlla se una cella è occupata.
     * @param {string} position Es. "c4"
     * @returns {boolean}
     */
    isOccupied(position) {
        return !!this.grid[position];
    }

    /**
     * Ottiene le posizioni adiacenti (ortogonalmente) a una data posizione.
     * @param {string} position Es. "c4"
     * @returns {string[]} Array di posizioni adiacenti valide.
     */
    getAdjacentPositions(position) {
        const adjacent = [];
        const colChar = position.charAt(0);
        const row = parseInt(position.substring(1));
        const col = colChar.charCodeAt(0) - 96; // a=1, b=2, ...

        const deltas = [
            { dr: -1, dc: 0 }, // Sopra
            { dr: 1, dc: 0 },  // Sotto
            { dr: 0, dc: -1 }, // Sinistra
            { dr: 0, dc: 1 }   // Destra
        ];

        for (const delta of deltas) {
            const newRow = row + delta.dr;
            const newCol = col + delta.dc;
            if (newRow >= 1 && newRow <= ROWS && newCol >= 1 && newCol <= COLS) {
                const newColChar = String.fromCharCode(96 + newCol);
                adjacent.push(`${newColChar}${newRow}`);
            }
        }
        return adjacent;
    }

    /**
     * Tenta di piazzare una carta sulla griglia.
     * Include controlli di validità, vittoria e loop.
     * @param {Card} card La carta da piazzare.
     * @param {string} position La posizione target (es. "c4").
     * @param {string} playerColor Colore del giocatore ("white" o "black").
     * @param {boolean} isLastCard Indica se è l'ultima carta del giocatore.
     * @returns {{success: boolean, reason?: string, gainedControl?: string, isWin?: boolean}}
     */
    placeCard(card, position, playerColor, isLastCard = false) {
        if (!this.isValidPosition(position)) {
            return { success: false, reason: "Posizione non valida." };
        }

        const isCorner = this.isCornerPosition(position);
        const cardOnTarget = this.getCardAt(position);
        const opponentColor = playerColor === 'white' ? 'black' : 'white';
        let isRibaltoneAttempt = false; // Flag per segnalare a Game.js

        if (isCorner) {
            console.log(`[Board placeCard] Tentativo piazzamento su angolo ${position}.`);
            if (cardOnTarget) { // Angolo Occupato
                if (cardOnTarget.ownerColor === opponentColor) {
                    // Tentativo di Ribaltone
                    console.log(`[Board placeCard] Rilevato tentativo Ribaltone su ${position} occupato da ${opponentColor}.`);
                    isRibaltoneAttempt = true;
                    // Permetti di procedere, la validazione avverrà in Game.js
                } else {
                    // Angolo occupato da sé stesso
                    console.log(`[Board placeCard] Blocco: Impossibile piazzare su angolo ${position} già occupato da te.`);
                    return { success: false, reason: "Non puoi piazzare su un angolo che già controlli." };
                }
            } else { // Angolo Vuoto (o meglio, non occupato da una carta fisica)
                const vertexId = this.getVertexIdForCorner(position);
                if (!vertexId) { // Sicurezza
                     console.error(`[Board placeCard] Errore: ${position} è angolo ma non ha vertexId?`);
                     return { success: false, reason: "Errore interno: angolo non valido." };
                }
                const currentVertexController = this.vertexControl[vertexId];

                if (isLastCard) {
                    // È l'ultima carta: controlla l'esclusiva
                    if (currentVertexController === null) {
                        // Vertice libero: OK per conquista ERA1
                        console.log(`[Board placeCard] Permesso piazzamento ultima carta su angolo LIBERO ${position} (Tentativo ERA1 - Conquista). Controller: ${currentVertexController}`);
                        // Procedi ai controlli successivi
                    } else if (currentVertexController === playerColor) {
                        // Vertice già controllato dal giocatore: OK (ma non sovrascrivere la carta se c'è)
                        console.log(`[Board placeCard] Permesso piazzamento ultima carta su angolo ${position} GIA' CONTROLLATO da ${playerColor}. Controller: ${currentVertexController}`);
                         // Procedi ai controlli successivi (il controllo isOccupied gestirà la sovrascrittura)
                    } else {
                        // Vertice controllato dall'avversario: NON OK per ultima carta
                        console.log(`[Board placeCard] Blocco: Impossibile piazzare ultima carta su angolo ${position} perché controllato da ${currentVertexController} (non hai l'esclusiva).`);
                        return { success: false, reason: `Non puoi piazzare l'ultima carta sul vertice ${position} perché non ne hai l'esclusiva.` };
                    }
                } else {
                    // Non è l'ultima carta: NON puoi piazzare su angolo non occupato fisicamente
                    console.log(`[Board placeCard] Blocco: Impossibile piazzare su angolo ${position} (non occupato fisicamente) se non è l'ultima carta.`);
                    return { success: false, reason: "Non puoi posizionare carte nei vertici non occupati fisicamente a meno che non sia la tua ultima carta." };
                }
            }
            // Se siamo qui, o è un tentativo di Ribaltone (angolo occupato da avversario)
            // o è l'ultima carta su angolo vuoto. Procedi con i controlli comuni.
        } else { // Non è un angolo
            if (cardOnTarget) {
                // Cella normale occupata
                console.log(`[Board placeCard] Blocco: Posizione normale ${position} già occupata.`);
                return { success: false, reason: "Posizione già occupata." };
            }
            // Cella normale vuota -> procedi con controlli standard
        }

        // --- Controlli Comuni (per piazzamenti validi permessi finora) ---
        // Se è un tentativo di Ribaltone, saltiamo alcuni controlli standard

        const adjacentPositions = this.getAdjacentPositions(position);
        const adjacentCards = adjacentPositions.map(pos => ({ pos, card: this.getCardAt(pos) })).filter(item => item.card);

        // Regola di Adiacenza: Deve essere piazzata vicino a un'altra carta (dopo la prima)
        if (this.initialCardPlaced && adjacentCards.length === 0) {
             return { success: false, reason: "La carta deve essere piazzata adiacente ad un'altra carta." };
        }

        // Regola di Blocco Adiacente: Non puoi piazzare una carta se una qualsiasi carta adiacente la batte.
        console.log(`[Board Validation] Checking placement of ${card} at ${position}`); // Log inizio validazione
        for (const item of adjacentCards) { // Itera su TUTTE le carte adiacenti
             console.log(`[Board Validation] Comparing adjacent ${item.card} at ${item.pos} against placing ${card}`); // Log carte confrontate
             // Se la carta adiacente (item.card) vince contro quella che stiamo piazzando (card)...
            const adjacentWins = item.card.winsAgainst(card);
            console.log(`[Board Validation] Does ${item.card} win against ${card}? ${adjacentWins}`); // Log risultato confronto
            if (adjacentWins) {
                 // ...allora il piazzamento non è valido.
                 console.log(`[Board Validation] Blocking placement because ${item.card} beats ${card}`); // Log blocco
                 return { success: false, reason: `Piazzamento bloccato: la carta ${card} viene battuta da ${item.card} in ${item.pos}.` };
            }
        }
        console.log(`[Board Validation] Placement of ${card} at ${position} passed adjacent checks.`); // Log successo validazione

        // Controllo Loop Hole
        const loopCheck = this.checkLoopHole(card, position, adjacentCards.map(item => item.card));
        if (loopCheck.isLoop) {
            return { success: false, reason: `Loop Hole rilevato: ${loopCheck.type}.` };
        }

        // Piazzamento valido!
        const cardToPlace = new Card(card.suit, card.value); // Crea istanza per aggiungere proprietà
        cardToPlace.ownerColor = playerColor; // Associa la carta al giocatore
        this.grid[position] = cardToPlace;
        console.log(`[BOARD PLACEMENT] Carta ${card.suit}-${card.value} salvata in posizione ${position} per giocatore ${playerColor}`);
        console.log(`[BOARD PLACEMENT] Griglia dopo piazzamento:`, JSON.stringify(this.grid));
        if (!this.initialCardPlaced) this.initialCardPlaced = true;

        // Aggiornamento controllo vertice per ultima carta su angolo libero

        if (isLastCard && isCorner) {
            const vertexId = this.getVertexIdForCorner(position);
            if (vertexId) {
                // Verifica se qualsiasi angolo è occupato (incluso quello che stiamo piazzando ora)
                const cornerPositions = {
                    "vertex-a1": ["a1"],
                    "vertex-f1": ["f1"],
                    "vertex-a6": ["a6"],
                    "vertex-f6": ["f6"]
                };

                let anyOtherCornerOccupied = false;

                // Prima controlla se c'era già una carta in questo angolo (prima del nostro piazzamento)
                const wasThisCornerOccupied = cardOnTarget !== null;

                // Poi controlla se c'è qualsiasi altro angolo occupato
                for (const [otherVertex, corners] of Object.entries(cornerPositions)) {
                    if (otherVertex !== vertexId) { // Non controllare l'angolo che stiamo piazzando
                        for (const corner of corners) {
                            if (this.isOccupied(corner)) {
                                console.log(`[Board placeCard] Angolo ${corner} è occupato, mentre stiamo piazzando su ${position} (vertice ${vertexId})`);
                                anyOtherCornerOccupied = true;
                                break;
                            }
                        }
                        if (anyOtherCornerOccupied) break;
                    }
                }

                // Nuova regola: Se c'è QUALSIASI angolo occupato (diverso da quello che stiamo piazzando),
                // il vertice rimane neutrale
                if (anyOtherCornerOccupied) {
                    console.log(`[Board placeCard] Altri angoli sono occupati, il vertice ${vertexId} rimane neutrale nonostante il piazzamento dell'ultima carta`);
                    // Se il vertice era già controllato, lo rilasciamo e lo rendiamo neutrale
                    if (this.vertexControl[vertexId] !== null) {
                        this.vertexControl[vertexId] = null;
                        console.log(`[Board placeCard] Vertice ${vertexId} è stato liberato e reso neutrale`);
                    }
                } else if (this.vertexControl[vertexId] === null) {
                    // Solo se non ci sono altri angoli occupati, il giocatore può conquistare il vertice
                    this.vertexControl[vertexId] = playerColor;
                    console.log(`[Board placeCard - Post Grid Update] Giocatore ${playerColor} ha conquistato il vertice ${vertexId} piazzando l'ultima carta.`);
                }
            }
        }


        // Controllo Conquista Vertice
        const gainedControl = this.updateVertexControl(cardToPlace, position, playerColor);

        // Controllo Vittoria (se è l'ultima carta)
        // Rimosso blocco if(isLastCard) perché la logica è gestita in Game.js


        return { success: true, gainedControl: gainedControl };
    }

    /**
     * Controlla la presenza di Loop Hole (Simbolico, Numerico o Ibrido).
     * @param {Card} cardToPlace La carta che si tenta di piazzare.
     * @param {string} position La posizione target.
     * @param {Card[]} adjacentCards Le carte effettivamente adiacenti.
     * @returns {{isLoop: boolean, type?: string, description?: string}}
     */
    checkLoopHole(cardToPlace, position, adjacentCards) {
        console.log(`[Board.checkLoopHole] INIZIO controllo loop in posizione ${position} con ${adjacentCards.length} carte adiacenti`);
        if (adjacentCards.length === 0) {
            console.log(`[Board.checkLoopHole] Nessuna carta adiacente in ${position}, nessun loop possibile`);
            return { isLoop: false }; // Non può esserci loop senza carte adiacenti
        }

        // Log delle carte adiacenti per debug
        adjacentCards.forEach((card, index) => {
            console.log(`[Board.checkLoopHole] Carta adiacente ${index+1}: ${card.value} di ${card.suit}`);
        });

        // --- Controllo Loop Numerico ---
        // Si verifica quando celle adiacenti contengono un Asso e un Re dello stesso seme,
        // più una carta di un altro seme vinta dall'altro giocatore.
        const aceCards = adjacentCards.filter(c => c.value === 'A');
        const kingCards = adjacentCards.filter(c => c.value === 'K');

        // Verifica se ci sono Assi e Re dello stesso seme
        for (const ace of aceCards) {
            for (const king of kingCards) {
                if (ace.suit === king.suit) {
                    // Verifica se c'è almeno una carta di un altro seme
                    const otherSuitCards = adjacentCards.filter(c => c.suit !== ace.suit);
                    if (otherSuitCards.length > 0) {
                        return {
                            isLoop: true,
                            type: "Numeric",
                            description: `Asso e Re di ${ace.suit} con ${otherSuitCards[0].value} di ${otherSuitCards[0].suit} creano un loop numerico.`
                        };
                    }
                }
            }
        }

        // --- Controllo Loop Simbolico ---
        // Si verifica quando almeno 3 carte con simboli diversi sono adiacenti a una cella vuota.
        const uniqueSuits = new Set(adjacentCards.map(card => card.suit));
        const uniqueSuitsArray = Array.from(uniqueSuits);
        console.log(`[Board.checkLoopHole] Controllo loop simbolico in posizione ${position}. Simboli unici: ${uniqueSuitsArray.join(', ')} (${uniqueSuits.size})`);

        // Verifica se ci sono almeno 3 simboli diversi
        if (uniqueSuits.size >= 3) {
            console.log(`[Board.checkLoopHole] TROVATO LOOP SIMBOLICO in ${position} con simboli: ${uniqueSuitsArray.join(', ')}`);

            // Forza la creazione di un loop simbolico per test
            const result = {
                isLoop: true,
                type: "Symbolic",
                description: `Tre simboli diversi (${uniqueSuitsArray.join(', ')}) creano un loop simbolico.`
            };

            console.log(`[Board.checkLoopHole] Restituisco risultato loop:`, JSON.stringify(result));
            return result;
        }

        // --- Controllo Loop Ibrido ---
        // Si verifica quando una cella è circondata da quattro carte contenenti:
        // - Un Asso e un Re dello stesso simbolo
        // - Due carte dello stesso simbolo vinte dal giocatore precedente
        if (adjacentCards.length >= 4) {
            // Verifica se ci sono Assi e Re
            if (aceCards.length > 0 && kingCards.length > 0) {
                // Conta le occorrenze di ogni simbolo
                const suitCounts = {};
                adjacentCards.forEach(card => {
                    suitCounts[card.suit] = (suitCounts[card.suit] || 0) + 1;
                });

                // Verifica se c'è un simbolo che appare almeno due volte (oltre ad Asso e Re)
                for (const suit in suitCounts) {
                    if (suitCounts[suit] >= 2) {
                        // Verifica se c'è un Asso e un Re dello stesso simbolo
                        const hasAceKingPair = aceCards.some(ace =>
                            kingCards.some(king => king.suit === ace.suit)
                        );
    console.log(`[Board] updateVertexControl chiamato per posizione: ${position}, giocatore: ${playerColor}`);


                        if (hasAceKingPair) {
                            return {
                                isLoop: true,
                                type: "Hybrid",
                                description: `Asso e Re con due carte di ${suit} creano un loop ibrido.`
                            };
                        }
                    }
                }
            }
        }

        // Se nessuno dei controlli sopra ha dato esito positivo, non è un loop.
        return { isLoop: false };
    }
    /**
     * Aggiorna il controllo dei vertici se la carta è piazzata sulla cella centrale corretta.
     * @param {Card} placedCard La carta appena piazzata.
     * @param {string} position Posizione della carta (es. "b2").
     * @param {string} playerColor Colore del giocatore ("white" o "black").
     * @returns {string | null} L'ID del vertice conquistato, o null.
     */
    updateVertexControl(placedCard, position, playerColor) {
        // Mappatura delle celle centrali ai vertici (aggiornata secondo le regole)
        const centralCells = {
            "c3": "vertex-a1", // Quadrante II -> Vertice a1 (White)
            "c4": "vertex-a6", // Quadrante III -> Vertice a6 (Black)
            "d3": "vertex-f1", // Quadrante I  -> Vertice f1 (White)
            "d4": "vertex-f6"  // Quadrante IV -> Vertice f6 (Black)
        };

        const targetVertexId = centralCells[position];

        if (targetVertexId) {
            // FIX: Rimuovo il controllo sul colore nativo del vertice e permetto
            // a qualsiasi giocatore di conquistare un vertice
            const currentController = this.vertexControl[targetVertexId]; // Recupera lo stato attuale
            console.log(`Tentativo di controllo del vertice ${targetVertexId} da ${playerColor}`);
            console.log(`Stato attuale del vertice: ${currentController}`);

            // Verifica se il vertice è uno di quelli che ha ricevuto una carta di setup iniziale
            if (this.setupVertices.has(targetVertexId)) {
                console.log(`Il vertice ${targetVertexId} ha ricevuto una carta di setup iniziale, rimane neutrale`);
                // Se il vertice era già controllato, lo rilasciamo e lo rendiamo neutrale
                if (currentController !== null) {
                    this.vertexControl[targetVertexId] = null;
                    console.log(`Vertice ${targetVertexId} è stato liberato e reso neutrale (era un vertice di setup)`);
                }
                // Non cambiamo lo stato del vertice, rimane neutrale
                return null;
            }

            // Per i vertici che NON hanno ricevuto una carta di setup, possono essere conquistati normalmente
            // Un vertice può essere conquistato solo se è attualmente libero (null)
            if (currentController === null) {
                this.vertexControl[targetVertexId] = playerColor;
                console.log(`Giocatore ${playerColor} ha conquistato il vertice ${targetVertexId} piazzando in ${position}`);
                return targetVertexId; // Ritorna l'ID del vertice conquistato
            } else {
                // Se è già controllato (da te o dall'avversario), non cambia nulla
                console.log(`Tentativo di conquista del vertice ${targetVertexId} fallito (già controllato da ${currentController})`);
            }
        }
        return null; // Nessun nuovo controllo acquisito
    }

     /**
      * Controlla se la plancia è piena.
      * @returns {boolean}
      */
    /**
     * Ottiene l'ID del vertice corrispondente a una posizione d'angolo.
     * @param {string} position La posizione dell'angolo (es. "a1").
     * @returns {string | null} L'ID del vertice (es. "vertex-a1") o null se la posizione non è un angolo valido.
     */
    getVertexIdForCorner(position) {
        const normalizedPosition = position.trim().toLowerCase();
        const cornerToVertexMap = {
            'a1': 'vertex-a1',
            'f1': 'vertex-f1',
            'a6': 'vertex-a6',
            'f6': 'vertex-f6'
        };
        return cornerToVertexMap[normalizedPosition] || null;
    }


     isFull() {
         return Object.values(this.grid).every(cell => cell !== null);
     }

    /**
     * Verifica se una posizione è un angolo della plancia (a1, f1, a6, f6).
     * @param {string} position La posizione da controllare.
     * @returns {boolean} True se la posizione è un angolo, false altrimenti.
     */
    isCornerPosition(position) {
        if (!position) return false;

        // Normalizza la posizione (rimuovi spazi, converti in minuscolo)
        const normalizedPosition = position.trim().toLowerCase();

        // Verifica esplicita per ogni angolo
        if (normalizedPosition === 'a1' || normalizedPosition === 'f1' ||
            normalizedPosition === 'a6' || normalizedPosition === 'f6') {
            console.log(`[Board] Posizione ${position} è un angolo!`);
            return true;
        }
        return false;
    }

    /**
     * Verifica se piazzare una carta in una posizione è una mossa valida, senza modificare lo stato.
     * Controlla adiacenza, vittoria contro avversari e loop hole.
     * @param {Card} card La carta da piazzare.
     * @param {string} position La posizione target (es. "c4").
     * @param {string} playerColor Colore del giocatore ("white" o "black").
     * @param {boolean} isLastCard Indica se è l'ultima carta del giocatore.
     * @returns {boolean} True se il piazzamento è valido, false altrimenti.
     */
    isValidPlacement(card, position, playerColor, isLastCard = false) {
        if (!this.isValidPosition(position) || this.isOccupied(position)) {
            return false;
        }

        // Regola dei vertici (angoli a1, f1, a6, f6)
        const isCorner = this.isCornerPosition(position);
        const cardOnTarget = this.getCardAt(position); // Controlla se l'angolo è occupato
        const opponentColor = playerColor === 'white' ? 'black' : 'white';

        if (isCorner) {
            console.log(`[Board isValidPlacement] Tentativo di piazzare in angolo ${position}, isLastCard=${isLastCard}`);

            if (cardOnTarget) { // Angolo Occupato
                if (cardOnTarget.ownerColor === opponentColor) {
                    // Tentativo di Ribaltone: SEMPRE VALIDO a questo livello (controllo forza in Game.js)
                    console.log(`[Board isValidPlacement] Tentativo Ribaltone su angolo occupato ${position}. Permesso (controllo forza in Game.js).`);
                    // Non ritornare false, procedi ai controlli di adiacenza/sconfitta/loop
                } else {
                    // Angolo occupato da sé stesso: NON VALIDO
                    console.log(`[Board isValidPlacement] Piazzamento in angolo ${position} NON valido (già occupato da te).`);
                    return false;
                }
            } else { // Angolo Vuoto (non occupato fisicamente)
                 const vertexId = this.getVertexIdForCorner(position);
                 if (!vertexId) { // Sicurezza
                     console.error(`[Board isValidPlacement] Errore: ${position} è angolo ma non ha vertexId?`);
                     return false;
                 }
                 const currentVertexController = this.vertexControl[vertexId];

                 if (isLastCard) {
                     // È l'ultima carta: controlla l'esclusiva
                     if (currentVertexController === null || currentVertexController === playerColor) {
                         // Vertice libero o già controllato dal giocatore: OK a questo livello
                         console.log(`[Board isValidPlacement] Piazzamento ultima carta su angolo ${position} (Controller: ${currentVertexController || 'nessuno'}) è potenzialmente valido (controllo esclusiva OK).`);
                         // Procedi ai controlli successivi
                     } else {
                         // Vertice controllato dall'avversario: NON OK per ultima carta
                         console.log(`[Board isValidPlacement] Piazzamento ultima carta su angolo ${position} NON valido (controllato da ${currentVertexController}, non hai l'esclusiva).`);
                         return false;
                     }
                 } else {
                     // Non è l'ultima carta: NON puoi piazzare su angolo non occupato fisicamente
                     console.log(`[Board isValidPlacement] Piazzamento in angolo ${position} (non occupato fisicamente) NON valido (non è l'ultima carta).`);
                     return false;
                 }
            }
        } else { // Non è un angolo
             if (cardOnTarget) { // Cella normale occupata
                 console.log(`[Board isValidPlacement] Piazzamento in cella normale occupata ${position} NON valido.`);
                 return false; // Non si può piazzare su celle normali occupate
             }
             // Se non è un angolo e la cella è vuota, procedi normalmente
        }


        // --- Controlli Comuni (per piazzamenti permessi finora) ---
        const adjacentPositions = this.getAdjacentPositions(position);
        const adjacentCards = adjacentPositions.map(pos => ({ pos, card: this.getCardAt(pos) })).filter(item => item.card);

        // Regola di Adiacenza: Deve essere piazzata vicino a un'altra carta (dopo la prima)
        if (this.initialCardPlaced && adjacentCards.length === 0) {
             return false;
        }

        // Regola di Blocco Adiacente: Non puoi piazzare una carta se una qualsiasi carta adiacente la batte.
        console.log(`[Board isValidPlacement] Checking placement of ${card} at ${position}`); // Log inizio validazione
        for (const item of adjacentCards) { // Itera su TUTTE le carte adiacenti
             console.log(`[Board isValidPlacement] Comparing adjacent ${item.card} at ${item.pos} against placing ${card}`); // Log carte confrontate
             // Se la carta adiacente (item.card) vince contro quella che stiamo piazzando (card)...
            const adjacentWins = item.card.winsAgainst(card);
             console.log(`[Board isValidPlacement] Does ${item.card} win against ${card}? ${adjacentWins}`); // Log risultato confronto
            if (adjacentWins) {
                 // ...allora il piazzamento non è valido.
                 console.log(`[Board isValidPlacement] Blocking placement because ${item.card} beats ${card}`); // Log blocco
                 return false;
            }
        }
        console.log(`[Board isValidPlacement] Placement of ${card} at ${position} passed adjacent checks.`); // Log successo validazione

        // Controllo Loop Hole
        const loopCheck = this.checkLoopHole(card, position, adjacentCards.map(item => item.card));
        if (loopCheck.isLoop) {
            return false;
        }

        // Se tutti i controlli passano, il piazzamento è valido
        return true;
    }

    // TODO: Aggiungere metodi per piazzare su vertici, controllare vittoria su vertici, etc.
}

module.exports = Board;