@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        
:root {
    --primary-color: #3955d4;
    --secondary-color: #6c5ce7;
    --accent-color: #ff7675;
    --dark-color: #2d3436;
    --light-color: #f8f9fa;
    --success-color: #00b894;
    --warning-color: #fdcb6e;
    --danger-color: #d63031;
    --text-color: #2d3436;
    --text-light: #636e72;
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    --shadow-light: 0 2px 15px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 15px 40px rgba(0, 0, 0, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: var(--light-color);
    color: var(--text-color);
    overflow-x: hidden;
}

.container {
    display: flex;
    position: relative;
}

/* Menu laterale fisso con effetto glassmorphism */
.skemino-sidebar {
    width: 280px;
    height: 100vh;
    background: rgba(45, 52, 54, 0.85);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: white;
    position: fixed;
    left: 0;
    top: 0;
    padding: 25px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 100;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) rgba(255, 255, 255, 0.1);
}

.skemino-sidebar::-webkit-scrollbar {
    width: 5px;
}

.skemino-sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.skemino-sidebar::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: 10px;
}

.skemino-logo-home {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}

.skemino-logo-image {
    max-width: 180px;
    cursor: pointer;
    transition: var(--transition);
    filter: drop-shadow(0 5px 15px rgba(106, 90, 205, 0.3));
}

.skemino-logo-image:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 5px 15px rgba(106, 90, 205, 0.6));
}

.skemino-sidebar nav {
    margin-bottom: 20px;
}

.skemino-sidebar nav ul {
    list-style-type: none;
}

.skemino-sidebar nav ul li {
    padding: 14px 16px;
    border-radius: 12px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.skemino-sidebar nav ul li::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: var(--transition);
    z-index: -1;
    border-radius: 12px;
}

.skemino-sidebar nav ul li:hover::before {
    width: 100%;
}

.skemino-sidebar nav ul li i {
    margin-right: 12px;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: var(--transition);
}

.skemino-sidebar nav ul li:hover i {
    transform: translateY(-2px);
}

.skemino-active {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 0 5px 15px rgba(106, 90, 205, 0.4);
}

.skemino-active i {
    transform: translateY(-2px);
}

.skemino-active::before {
    width: 100% !important;
}

.skemino-sidebar-footer {
    margin-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
}

.skemino-auth-btn {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 14px 16px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    text-align: left;
    font-weight: 500;
    box-shadow: 0 4px 10px rgba(106, 90, 205, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.skemino-auth-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
    z-index: -1;
}

.skemino-auth-btn:hover::before {
    left: 100%;
}

.skemino-auth-btn i {
    margin-right: 12px;
    font-size: 16px;
}

.skemino-auth-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(106, 90, 205, 0.4);
}

.skemino-user-info {
    color: white;
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 12px;
    backdrop-filter: blur(5px);
}

.skemino-sidebar-controls {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.skemino-lang-switch, .skemino-support-btn {
    flex: 1;
    min-width: 100px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.skemino-lang-switch i, .skemino-support-btn i {
    margin-right: 8px;
}

.skemino-lang-switch:hover, .skemino-support-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Contenuto principale */
.skemino-main-wrapper {
    margin-left: 280px;
    width: calc(100% - 280px);
    min-height: 100vh;
    position: relative;
}

.skemino-main-content {
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Effetto Particles background */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

.particle {
    position: absolute;
    background: rgba(106, 90, 205, 0.15);
    border-radius: 50%;
    animation: float 15s infinite ease-in-out;
}

@keyframes float {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(-1000px) rotate(720deg);
        opacity: 0;
    }
}

/* Stili comuni per le sezioni */
section {
    width: 100%;
    max-width: 1100px;
    margin-bottom: 60px;
    background-color: white;
    border-radius: 20px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    transition: var(--transition);
    transform: translateY(0);
}

section:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

/* Sezione Hero */
.skemino-hero-section {
    display: flex;
    padding: 0;
    position: relative;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    z-index: 1;
    overflow: hidden;
}

.skemino-hero-section::before {
    content: "";
    position: absolute;
    bottom: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
    opacity: 0.15;
    z-index: -1;
}

.skemino-hero-section::after {
    content: "";
    position: absolute;
    top: -50px;
    left: -50px;
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, var(--secondary-color) 0%, transparent 70%);
    opacity: 0.15;
    z-index: -1;
}

.skemino-board-preview {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    position: relative;
    overflow: hidden;
}

.skemino-board-preview::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(106, 90, 205, 0.1), transparent);
    z-index: -1;
}

.skemino-tabellone-image {
    position: relative;
    transition: var(--transition);
    transform-style: preserve-3d;
    perspective: 1000px;
}

.skemino-tabellone-image:hover {
    transform: rotateY(5deg) rotateX(5deg);
}

.skemino-tabellone-img {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    transition: var(--transition);
    transform: translateZ(0);
}

.skemino-tabellone-image:hover .skemino-tabellone-img {
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.skemino-hero-content {
    flex: 1;
    padding: 50px 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    z-index: 2;
}

.skemino-hero-title-container h1 {
    color: var(--dark-color);
    margin-bottom: 15px;
    font-size: 42px;
    font-weight: 700;
    letter-spacing: -0.5px;
    line-height: 1.2;
    background: linear-gradient(to right, var(--dark-color), var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.skemino-hero-title-container h2 {
    color: var(--text-light);
    font-size: 20px;
    font-weight: 400;
    line-height: 1.5;
    margin-bottom: 30px;
}

.skemino-hero-actions-container {
    margin-top: 30px;
}

.skemino-primary-btn {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 18px 20px;
    margin-bottom: 15px;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: var(--transition);
    color: white;
    text-align: left;
    position: relative;
    overflow: hidden;
    z-index: 1;
    text-decoration: none;
}

.skemino-primary-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
    z-index: -1;
}

.skemino-primary-btn:hover::before {
    left: 100%;
}

.skemino-online-btn {
    background: linear-gradient(135deg, var(--success-color), #20bf6b);
    box-shadow: 0 8px 15px rgba(0, 184, 148, 0.3);
}

.skemino-online-btn:hover {
    box-shadow: 0 10px 20px rgba(0, 184, 148, 0.4);
    transform: translateY(-3px);
}

.skemino-training-btn {
    background: linear-gradient(135deg, #fdcb6e, #e67e22);
    box-shadow: 0 8px 15px rgba(230, 126, 34, 0.3);
}

.skemino-training-btn:hover {
    box-shadow: 0 10px 20px rgba(230, 126, 34, 0.4);
    transform: translateY(-3px);
}

.skemino-primary-btn i {
    font-size: 24px;
    margin-right: 15px;
    background: rgba(255, 255, 255, 0.2);
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    transition: var(--transition);
}

.skemino-primary-btn:hover i {
    transform: rotate(10deg);
    background: rgba(255, 255, 255, 0.3);
}

.skemino-button-content {
    display: flex;
    flex-direction: column;
}

.skemino-primary-text {
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 3px;
}

.skemino-secondary-text {
    font-size: 14px;
    opacity: 0.9;
}

/* Sezione Regole */
.skemino-rules-section {
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.skemino-section-header {
    text-align: center;
    padding: 35px 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    position: relative;
    overflow: hidden;
}

.skemino-section-header::before, 
.skemino-section-header::after {
    content: "";
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.skemino-section-header::before {
    top: -100px;
    right: -50px;
}

.skemino-section-header::after {
    bottom: -100px;
    left: -50px;
}

.skemino-section-header h2 {
    margin-bottom: 10px;
    font-size: 28px;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.skemino-section-header p {
    font-size: 16px;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

.skemino-rules-content {
    display: flex;
    padding: 0;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
}

.skemino-rules-video {
    flex: 1;
    padding: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.skemino-rules-video::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(106, 90, 205, 0.05), transparent);
    z-index: 0;
}

.skemino-rules-video iframe {
    width: 100%;
    height: 300px;
    border-radius: 15px;
    box-shadow: var(--shadow-medium);
    position: relative;
    z-index: 1;
    transition: var(--transition);
}

.skemino-rules-video:hover iframe {
    transform: scale(1.02);
    box-shadow: var(--shadow-heavy);
}

.skemino-rules-text {
    flex: 1;
    padding: 40px;
    position: relative;
}

blockquote {
    border-left: 5px solid var(--primary-color);
    padding: 20px 25px;
    margin: 0 0 30px 0;
    font-style: italic;
    color: var(--text-light);
    background: rgba(106, 90, 205, 0.05);
    border-radius: 0 15px 15px 0;
    position: relative;
}

blockquote::before {
    content: '"';
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 40px;
    color: rgba(106, 90, 205, 0.2);
    font-family: Georgia, serif;
}

blockquote cite {
    display: block;
    text-align: right;
    font-size: 15px;
    margin-top: 10px;
    font-weight: 500;
    color: var(--primary-color);
}

.skemino-rules-text p {
    line-height: 1.7;
    margin-bottom: 20px;
    color: var(--text-color);
}

.skemino-rules-text strong {
    color: var(--dark-color);
    font-weight: 600;
}

.skemino-learn-more-btn {
    display: inline-block;
    padding: 14px 28px;
    margin-top: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(106, 90, 205, 0.3);
    position: relative;
    overflow: hidden;
}

.skemino-learn-more-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.skemino-learn-more-btn:hover::before {
    left: 100%;
}

.skemino-learn-more-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(106, 90, 205, 0.4);
}

/* Sezione Academy */
.skemino-academy-section {
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.skemino-academy-header {
    text-align: center;
    padding: 35px 40px;
    background: linear-gradient(135deg, #8e44ad, #9b59b6);
    color: white;
    position: relative;
    overflow: hidden;
}

.skemino-academy-header::before,
.skemino-academy-header::after {
    content: "";
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.skemino-academy-header::before {
    top: -100px;
    right: -50px;
}

.skemino-academy-header::after {
    bottom: -100px;
    left: -50px;
}

.skemino-academy-title {
    margin-bottom: 10px;
    font-size: 28px;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.skemino-academy-subtitle {
    font-size: 16px;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

.skemino-features-section {
    display: flex;
    padding: 40px;
    gap: 25px;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
}

.skemino-feature-card {
    flex: 1;
    padding: 30px 25px;
    background-color: white;
    border-radius: 15px;
    text-align: center;
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
    z-index: 1;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.skemino-feature-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: var(--transition);
}

.skemino-feature-card:hover::before {
    opacity: 1;
}

.skemino-feature-card::after {
    content: "";
    position: absolute;
    bottom: -5px;
    right: -5px;
    width: 15px;
    height: 15px;
    background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
    opacity: 0.2;
    z-index: -1;
    transition: var(--transition);
}

.skemino-feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
    border-color: rgba(0, 0, 0, 0);
}

.skemino-feature-card:hover::after {
    width: 300px;
    height: 300px;
    opacity: 0.05;
}

.skemino-feature-card i {
    font-size: 40px;
    color: var(--primary-color);
    margin-bottom: 20px;
    transition: var(--transition);
    display: inline-block;
    background: rgba(106, 90, 205, 0.1);
    width: 80px;
    height: 80px;
    line-height: 80px;
    border-radius: 50%;
}

.skemino-feature-card:hover i {
    transform: rotateY(360deg);
    color: var(--secondary-color);
    background: rgba(106, 90, 205, 0.15);
}

.skemino-feature-card h3 {
    margin-bottom: 15px;
    color: var(--dark-color);
    font-size: 20px;
    font-weight: 600;
    transition: var(--transition);
}

.skemino-feature-card p {
    margin-bottom: 20px;
    color: var(--text-light);
    line-height: 1.6;
    font-size: 15px;
}

.skemino-read-more {
    color: var(--primary-color);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    display: inline-block;
}

.skemino-read-more::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.skemino-feature-card:hover .skemino-read-more::after {
    width: 100%;
}

/* Aggiunta: Barra di stato superiore */
.skemino-status-bar {
    position: fixed;
    top: 0;
    right: 0;
    width: calc(100% - 280px);
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 40px;
    z-index: 99;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.skemino-status-bar-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.skemino-status-item {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    background: white;
    border-radius: 20px;
    font-size: 14px;
    color: var(--text-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: var(--transition);
}

.skemino-status-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.skemino-status-item i {
    margin-right: 8px;
    color: var(--primary-color);
}

.skemino-online-status {
    width: 10px;
    height: 10px;
    background-color: var(--success-color);
    border-radius: 50%;
    margin-right: 8px;
}

/* Aggiunta: Floating Action Button */
.skemino-fab {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 5px 20px rgba(106, 90, 205, 0.4);
    z-index: 99;
    transition: var(--transition);
}

.skemino-fab:hover {
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 8px 25px rgba(106, 90, 205, 0.5);
}

.skemino-fab i {
    font-size: 24px;
}

/* Responsive design */
@media (max-width: 1200px) {
    .skemino-features-section {
        flex-direction: column;
    }
    
    .skemino-feature-card {
        margin-bottom: 20px;
    }
}

@media (max-width: 992px) {
    .skemino-hero-section, .skemino-rules-content {
        flex-direction: column;
    }
    
    .skemino-status-bar {
        width: calc(100% - 70px);
    }
    
    .skemino-main-wrapper {
        margin-left: 70px;
        width: calc(100% - 70px);
    }
}

@media (max-width: 768px) {
    .skemino-sidebar {
        width: 70px;
        padding: 20px 5px;
    }
    
    .skemino-sidebar nav ul li span, 
    .skemino-logo-home,
    .skemino-sidebar-footer {
        display: none;
    }
    
    .skemino-sidebar nav ul li {
        justify-content: center;
        padding: 15px 0;
    }
    
    .skemino-sidebar nav ul li i {
        margin-right: 0;
    }
    
    .skemino-main-content {
        padding: 20px;
    }
    
    .skemino-hero-title-container h1 {
        font-size: 32px;
    }
    
    .skemino-hero-title-container h2 {
        font-size: 16px;
    }
}

/* Fix per le etichette scure nei popup */
.game-modal .form-group label {
    color: #333 !important; /* Colore scuro per buona leggibilità */
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.game-modal .form-group label i {
    color: #3498db; /* Colore blu per le icone */
    margin-right: 8px;
}

/* Se necessario, aggiungiamo anche uno sfondo chiaro al modal */
.game-modal .modal-content {
    background-color: #fff;
    color: #333;
}

/* Assicuriamoci che anche i placeholder siano visibili */
.game-modal input::placeholder {
    color: #999;
}
