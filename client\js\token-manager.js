/**
 * Token Manager - Gestisce automaticamente il refresh dei token JWT
 */

class TokenManager {
    constructor() {
        this.refreshPromise = null;
        this.isRefreshing = false;
    }

    /**
     * Controlla se il token è scaduto o sta per scadere
     * @param {string} token - Il token JWT
     * @returns {boolean} - True se il token è scaduto o sta per scadere
     */
    isTokenExpired(token) {
        if (!token) return true;

        try {
            // Decodifica il token senza verificarlo
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Math.floor(Date.now() / 1000);
            
            // Considera il token scaduto se mancano meno di 5 minuti alla scadenza
            const bufferTime = 5 * 60; // 5 minuti
            return payload.exp < (currentTime + bufferTime);
        } catch (error) {
            console.error('[TOKEN MANAGER] Errore nella decodifica del token:', error);
            return true;
        }
    }

    /**
     * Rinnova il token JWT
     * @returns {Promise<string|null>} - Il nuovo token o null se il rinnovo fallisce
     */
    async refreshToken() {
        // Se è già in corso un refresh, aspetta quello
        if (this.isRefreshing && this.refreshPromise) {
            return await this.refreshPromise;
        }

        this.isRefreshing = true;
        this.refreshPromise = this._performRefresh();

        try {
            const result = await this.refreshPromise;
            return result;
        } finally {
            this.isRefreshing = false;
            this.refreshPromise = null;
        }
    }

    /**
     * Esegue effettivamente il refresh del token
     * @private
     */
    async _performRefresh() {
        try {
            console.log('[TOKEN MANAGER] Tentativo di rinnovo token...');
            
            const response = await fetch('/api/auth/refresh-token', {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.token) {
                    console.log('[TOKEN MANAGER] Token rinnovato con successo');
                    
                    // Aggiorna il token nel localStorage
                    localStorage.setItem('token', data.token);
                    
                    // Aggiorna anche i dati utente se presenti
                    if (data.user) {
                        localStorage.setItem('user', JSON.stringify(data.user));
                        localStorage.setItem('loggedInUser', data.user.username);
                    }

                    // Reinizializza il socket con il nuovo token
                    if (window.reinitializeSocket && typeof window.reinitializeSocket === 'function') {
                        console.log('[TOKEN MANAGER] Reinizializzazione socket con nuovo token');
                        window.reinitializeSocket();
                    }

                    return data.token;
                } else {
                    console.error('[TOKEN MANAGER] Risposta di refresh non valida:', data);
                    return null;
                }
            } else {
                console.error('[TOKEN MANAGER] Errore HTTP durante il refresh:', response.status);
                return null;
            }
        } catch (error) {
            console.error('[TOKEN MANAGER] Errore durante il refresh del token:', error);
            return null;
        }
    }

    /**
     * Ottiene un token valido, rinnovandolo se necessario
     * @returns {Promise<string|null>} - Un token valido o null se non disponibile
     */
    async getValidToken() {
        let token = localStorage.getItem('token');
        
        if (!token) {
            console.log('[TOKEN MANAGER] Nessun token presente');
            return null;
        }

        if (this.isTokenExpired(token)) {
            console.log('[TOKEN MANAGER] Token scaduto, tentativo di rinnovo...');
            token = await this.refreshToken();
            
            if (!token) {
                console.log('[TOKEN MANAGER] Impossibile rinnovare il token, redirect al login');
                this.redirectToLogin();
                return null;
            }
        }

        return token;
    }

    /**
     * Reindirizza l'utente alla pagina di login
     */
    redirectToLogin() {
        // Pulisci i dati di autenticazione
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('loggedInUser');
        localStorage.removeItem('tokenExpiration');

        // Disconnetti il socket se presente
        if (window.socket && window.socket.connected) {
            window.socket.disconnect();
        }

        // Reindirizza al login
        window.location.href = '/login?expired=true';
    }

    /**
     * Controlla periodicamente lo stato del token
     */
    startTokenCheck() {
        // Controlla ogni 2 minuti
        setInterval(async () => {
            const token = localStorage.getItem('token');
            if (token && this.isTokenExpired(token)) {
                console.log('[TOKEN MANAGER] Token check: token scaduto, tentativo di rinnovo automatico');
                await this.getValidToken();
            }
        }, 2 * 60 * 1000); // 2 minuti
    }
}

// Crea un'istanza globale del token manager
window.tokenManager = new TokenManager();

// Avvia il controllo periodico del token
window.tokenManager.startTokenCheck();

console.log('[TOKEN MANAGER] Token Manager inizializzato');
