/**
 * online-game.js
 * Gestione specifica per partite online (multiplayer)
 * 
 * DETTAGLI DEL FUNZIONAMENTO:
 * 
 * 1. SCOPO:
 *    - Gestisce la logica specifica delle partite online
 *    - Mappa i giocatori remoti ai giocatori locali (white/black)
 *    - Sincronizza lo stato di gioco tra client e server
 *    - Gestisce la comunicazione delle mosse al server
 * 
 * 2. INIZIALIZZAZIONE:
 *    - Viene istanziato da game-mode-manager.js quando si avvia una partita online
 *    - Riceve socket e playerId dal GameModeManager
 *    - Si registra per ricevere aggiornamenti di stato dal server
 * 
 * 3. GESTIONE STATO:
 *    - updateGameState(): riceve nuovo stato dal server
 *    - Mappa gli ID dei giocatori remoti ai colori locali
 *    - Determina quale giocatore è l'avversario
 *    - Sincronizza le mani dei giocatori
 *    - Gestisce il mapping delle mosse
 * 
 * 4. COMUNICAZIONE:
 *    - sendCardPlacement(): invia posizionamento carta al server
 *    - handleOpponentMove(): processa mosse dell'avversario
 *    - Gestisce eventi di disconnessione/riconnessione
 * 
 * 5. INTEGRAZIONE:
 *    - Lavora con multiplayer.js per la comunicazione socket
 *    - Coordinato da game-mode-manager.js
 *    - Usa script.js per l'aggiornamento dell'UI
 */

class OnlineGameManager {
    constructor() {
        this.gameState = null;
        this.socket = null;
        this.myPlayerId = null;
        this.opponentId = null;
        this.gameId = null;
    }

    /**
     * Inizializza una partita online
     * @param {Socket} socket - Socket.io instance
     * @param {string} myPlayerId - ID del giocatore locale
     */
    initialize(socket, myPlayerId) {
        this.socket = socket;
        this.myPlayerId = myPlayerId;
        
        console.log('[ONLINE GAME] Inizializzazione partita online');
        console.log('[ONLINE GAME] Il mio ID:', myPlayerId);
        
        // Crea gli slot per le carte nelle aree delle mani per la modalità online
        if (typeof window.createHandSlots === 'function') {
            console.log('[ONLINE GAME] Creazione slot per le carte nelle aree delle mani');
            window.createHandSlots();
        } else {
            console.warn('[ONLINE GAME] Funzione createHandSlots non disponibile');
        }
    }

    /**
     * Gestisce l'aggiornamento dello stato di gioco
     * @param {Object} state - Nuovo stato di gioco dal server
     */
    updateGameState(state) {
        console.log('[ONLINE GAME] Aggiornamento stato di gioco');
        console.log('[ONLINE GAME] currentPlayerId BEFORE mapping:', state.currentPlayerId);
        
        this.gameState = state;
        this.gameId = state.gameId;
        
        // Trova l'ID dell'avversario
        if (state.players) {
            this.opponentId = Object.keys(state.players).find(id => id !== this.myPlayerId);
            console.log('[ONLINE GAME] ID avversario:', this.opponentId);
        }
        
        // Gestisci mapping dei giocatori per online game
        this.mapOnlinePlayers(state);
        
        console.log('[ONLINE GAME] currentPlayerId AFTER mapping:', state.currentPlayerId);
        console.log('[ONLINE GAME] originalCurrentPlayerId saved as:', state.originalCurrentPlayerId);
        
        return state;
    }

    /**
     * Mappa i giocatori online basandosi sul colore
     * @param {Object} state - Stato di gioco
     */
    mapOnlinePlayers(state) {
        if (!state.players || !this.myPlayerId || !this.opponentId) return;
        
        const localPlayerData = state.players[this.myPlayerId];
        const opponentPlayerData = state.players[this.opponentId];
        
        // Assegna i dati in base al colore (bianco = player1, nero = player2)
        if (localPlayerData && opponentPlayerData) {
            // SALVA i socket ID originali prima della trasformazione per uso nelle mosse
            state.originalCurrentPlayerId = state.currentPlayerId;
            state.mySocketId = this.myPlayerId;
            state.opponentSocketId = this.opponentId;
            
            // Crea la struttura players con player1/player2 per compatibilità con animazioni
            state.players = state.players || {};
            
            if (localPlayerData.color === 'white') {
                state.players.player1 = localPlayerData;
                state.players.player2 = opponentPlayerData;
                state.player1Data = localPlayerData;
                state.player2Data = opponentPlayerData;
            } else {
                state.players.player1 = opponentPlayerData;
                state.players.player2 = localPlayerData;
                state.player1Data = opponentPlayerData;
                state.player2Data = localPlayerData;
            }
            
            // Trasforma anche currentPlayerId per essere compatibile con player1/player2
            if (state.currentPlayerId === this.myPlayerId) {
                state.currentPlayerId = localPlayerData.color === 'white' ? 'player1' : 'player2';
            } else if (state.currentPlayerId === this.opponentId) {
                state.currentPlayerId = opponentPlayerData.color === 'white' ? 'player1' : 'player2';
            }
            
            // Assicurati che window.myPlayerId sia impostato correttamente per handleGameState
            if (!window.myPlayerId) {
                window.myPlayerId = this.myPlayerId;
                console.log('[ONLINE GAME] Impostato window.myPlayerId =', window.myPlayerId);
            }
            
            console.log('[ONLINE GAME] Player mapping:');
            console.log('[ONLINE GAME] - Player 1 (white):', state.players.player1?.id);
            console.log('[ONLINE GAME] - Player 2 (black):', state.players.player2?.id);
            console.log('[ONLINE GAME] - Current player ID transformed:', state.currentPlayerId);
            console.log('[ONLINE GAME] - Original current player ID (socket):', state.originalCurrentPlayerId);
        }
    }

    /**
     * Gestisce il posizionamento di una carta
     * @param {Object} card - Carta da posizionare
     * @param {string} position - Posizione sul tabellone
     */
    placeCard(card, position) {
        if (!this.socket || !this.gameId) return;
        
        console.log('[ONLINE GAME] Posizionamento carta:', card, 'in', position);
        
        // Per online game, usa gameAction
        this.socket.emit('gameAction', {
            action: 'placeCard',
            gameId: this.gameId,
            playerId: window.myPlayerId || this.socket.id, // Invia il playerId persistente
            actionData: {
                card: card,
                position: position
            }
        });
    }

    /**
     * Gestisce la pesca di una carta
     */
    drawCard() {
        if (!this.socket || !this.gameId) return;
        
        console.log('[ONLINE GAME] Pesca carta');
        
        // Per online game, usa gameAction
        this.socket.emit('gameAction', {
            action: 'drawCard',
            gameId: this.gameId,
            playerId: window.myPlayerId || this.socket.id, // Invia il playerId persistente
            actionData: {}
        });
    }

    /**
     * Gestisce l'abbandono della partita
     */
    leaveGame() {
        if (!this.socket || !this.gameId) return;
        
        console.log('[ONLINE GAME] Abbandono partita');
        
        this.socket.emit('leaveGame', {
            gameId: this.gameId
        });
        
        this.reset();
    }

    /**
     * Reset completo del manager
     */
    reset() {
        this.gameState = null;
        this.gameId = null;
        this.opponentId = null;
        console.log('[ONLINE GAME] Reset stato di gioco');
    }

    /**
     * Verifica se è il mio turno
     * @returns {boolean}
     */
    isMyTurn() {
        if (!this.gameState) return false;
        
        // Usa il socket ID originale per il controllo del turno
        const originalCurrentPlayerId = this.gameState.originalCurrentPlayerId;
        if (originalCurrentPlayerId) {
            return originalCurrentPlayerId === this.myPlayerId;
        }
        
        // Fallback per la logica precedente
        return this.gameState.currentPlayerId === this.myPlayerId;
    }

    /**
     * Ottiene i dati del giocatore locale
     * @returns {Object|null}
     */
    getMyPlayerData() {
        if (!this.gameState || !this.gameState.players) return null;
        
        return this.gameState.players[this.myPlayerId];
    }

    /**
     * Ottiene i dati dell'avversario
     * @returns {Object|null}
     */
    getOpponentData() {
        if (!this.gameState || !this.gameState.players || !this.opponentId) return null;
        
        return this.gameState.players[this.opponentId];
    }
}

// Crea un'istanza globale del manager
window.onlineGameManager = new OnlineGameManager();

console.log('[ONLINE GAME] Manager caricato e pronto');