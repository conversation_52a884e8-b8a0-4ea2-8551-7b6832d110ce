// Script specifico per la pagina home-logged.html
document.addEventListener('DOMContentLoaded', function() {
    // Verifica prima nel localStorage se l'utente è loggato
    const loggedInUser = localStorage.getItem('loggedInUser');
    const userData = localStorage.getItem('user');
    const token = localStorage.getItem('token');
    
    // Verifica che il token esista e sia valido
    if (loggedInUser && userData && token) {
        // L'utente è loggato, procedi
        loadUserDataFromStorage();
        loadRecentGames();
        
        // Inizializza il socket per il matchmaking
        if (typeof initializeSocket === 'function') {
            console.log('[HOME-LOGGED] Token nel localStorage:', localStorage.getItem('token') ? 'Present' : 'Missing');
            
            // Forza la reinizializzazione del socket con il token corretto
            if (window.socket) {
                console.log('[HOME-LOGGED] Socket esistente, disconnessione prima di reinizializzare');
                window.socket.disconnect();
            }
            
            initializeSocket();
            
            // Verifica che il socket sia stato inizializzato con il token
            setTimeout(() => {
                if (window.socket && window.socket.auth) {
                    console.log('[HOME-LOGGED] Socket auth dopo inizializzazione:', window.socket.auth);
                }
            }, 100);
        }
        
        // Carica le partite giornaliere all'avvio
        loadDailyMatches();
        
        // Carica il tab attivo di default per cronologia/statistiche
        setTimeout(() => {
            const activeTab = document.querySelector('.tab-button.active');
            if (activeTab) {
                const tabName = activeTab.getAttribute('data-tab');
                if (tabName === 'history') {
                    loadMatchHistory();
                } else if (tabName === 'stats') {
                    loadStatistics();
                }
            }
        }, 100);
        
        setupEventListeners();
    } else {
        // Se non ci sono dati nel localStorage, controlla authUtils
        if (window.authUtils && window.authUtils.isLoggedIn) {
            const isLogged = window.authUtils.isLoggedIn();
            
            // Se l'utente non è loggato, reindirizza alla home page principale
            if (!isLogged) {
                window.location.href = '/';
                return;
            }
            
            // Carica i dati dell'utente loggato
            loadUserData();
            
            // Carica le partite recenti
            loadRecentGames();
            
            // Inizializza il socket per il matchmaking
            if (typeof initializeSocket === 'function') {
                initializeSocket();
            }
            
            // Carica le partite giornaliere all'avvio
            loadDailyMatches();
            
            // Carica il tab attivo di default per cronologia/statistiche
            const activeTab = document.querySelector('.tab-button.active');
            if (activeTab) {
                const tabName = activeTab.getAttribute('data-tab');
                if (tabName === 'history') {
                    loadMatchHistory();
                } else if (tabName === 'stats') {
                    loadStatistics();
                }
            }
            
            // Gestisci bottoni e click
            setupEventListeners();
        } else {
            // Fallback nel caso in cui authUtils non sia ancora disponibile
            setTimeout(checkAuthUtils, 100);
        }
    }
});

// Verifica se c'è un redirect da effettuare dalla home principale
document.addEventListener('DOMContentLoaded', function() {
    // Controlla se ci sono parametri di gioco attivi (in questo caso non fare redirect)
    const urlParams = new URLSearchParams(window.location.search);
    
    // Se ci sono parametri da preservare, li trasferisci durante il redirect
    if (urlParams.has('gameId') || urlParams.has('resumeMultiplayer') || urlParams.has('action')) {
        // Non fa nulla, lascia che la pagina gestisca i parametri di gioco
        console.log('[REDIRECT] Parametri di gioco rilevati, nessun redirect necessario');
    }
});

/**
 * Carica i dati utente dal localStorage
 */
function loadUserDataFromStorage() {
    try {
        const userData = JSON.parse(localStorage.getItem('user') || '{}');
        if (userData.username) {
            // Aggiorna l'UI con i dati dell'utente
            updateUIWithUserData(userData);
        }
    } catch (error) {
        console.error('Errore nel parsing dei dati utente:', error);
    }
}

/**
 * Aggiorna l'interfaccia con i dati dell'utente
 */
function updateUIWithUserData(user) {
    if (!user) return;
    
    // Chiama la funzione esistente loadUserData
    loadUserData();
    
    // Aggiorna anche la hero section
    updateHeroSection(user);
}

/**
 * Controlla se authUtils è disponibile e riprova se non lo è
 */
function checkAuthUtils() {
    // Prima controlla nel localStorage
    const loggedInUser = localStorage.getItem('loggedInUser');
    if (loggedInUser) {
        loadUserDataFromStorage();
        loadRecentGames();
        setupEventListeners();
        return;
    }
    
    if (window.authUtils && window.authUtils.isLoggedIn) {
        const isLogged = window.authUtils.isLoggedIn();
        
        if (!isLogged) {
            window.location.href = '/';
            return;
        }
        
        // Carica i dati dell'utente loggato
        loadUserData();
        loadRecentGames();
        setupEventListeners();
    } else {
        setTimeout(checkAuthUtils, 100);
    }
}

/**
 * Carica i dati dell'utente loggato
 */
function loadUserData() {
    const user = window.authUtils.getCurrentUser();
    if (!user) return;
    
    // Mostra informazioni nel profilo della sidebar
    updateSidebarProfile(user);
    
    // Aggiorna la hero section
    updateHeroSection(user);
    
    // Mostra le feature boxes per utenti loggati
    const featureBoxes = document.querySelector('.feature-boxes');
    if (featureBoxes) {
        featureBoxes.style.display = 'flex';
    }
}



/**
 * Aggiorna il profilo nella sidebar
 */
function updateSidebarProfile(user) {
    // Aggiorna username nella sidebar
    const usernameDisplay = document.getElementById('username-display');
    if (usernameDisplay) {
        usernameDisplay.textContent = user.username || 'Giocatore';
    }
    
    // Aggiorna rank nella sidebar
    const userRank = document.getElementById('user-rank');
    if (userRank) {
        const rating = user.rating || 1000;
        let rankName = 'Principiante';
        
        if (rating >= 2700) {
            rankName = 'Super Gran Maestro';
        } else if (rating >= 2500) {
            rankName = 'Gran Maestro';
        } else if (rating >= 2400) {
            rankName = 'Maestro Internazionale';
        } else if (rating >= 2200) {
            rankName = 'Maestro';
        } else if (rating >= 2000) {
            rankName = 'Candidato Maestro';
        } else if (rating >= 1800) {
            rankName = 'Dilettante A';
        } else if (rating >= 1600) {
            rankName = 'Dilettante B';
        } else if (rating >= 1400) {
            rankName = 'Dilettante C';
        } else if (rating >= 1200) {
            rankName = 'Dilettante D';
        }
        
        userRank.textContent = rankName;
    }
    
    // Mostra il profilo utente e nascondi i pulsanti di autenticazione
    const authButtons = document.getElementById('auth-buttons');
    const userProfile = document.getElementById('user-profile');
    
    if (authButtons) {
        authButtons.style.display = 'none';
    }
    
    if (userProfile) {
        userProfile.style.display = 'block';
    }
    
    // Aggiorna tooltip dell'utente nel menu di gioco
    const userTooltip = document.getElementById('user-tooltip-text');
    if (userTooltip) {
        userTooltip.textContent = user.username || 'Utente';
    }
}

/**
 * Carica le partite recenti dell'utente
 */
function loadRecentGames() {
    // Questa funzione verrà implementata per caricare le partite recenti dell'utente
    // Richiederà un'integrazione con il backend per ottenere i dati delle partite
    
    // Implementazione di esempio per mostrare la struttura
    const user = window.authUtils.getCurrentUser();
    if (!user) return;
    
    // In una versione reale, questa sarà una chiamata API al backend
    console.log('Caricamento partite recenti per utente:', user.username);
    
    // Le partite recenti possono essere visualizzate in una sezione apposita nella pagina
}

/**
 * Aggiorna la hero section con i dati dell'utente
 */
function updateHeroSection(user) {
    if (!user) return;
    
    // Aggiorna il nome utente nella hero
    const heroUsername = document.getElementById('hero-username');
    if (heroUsername) {
        heroUsername.textContent = user.username || 'Giocatore';
    }
    
    // Calcola e imposta il livello/rank in base al rating
    const rating = user.rating || 1000;
    const heroRank = document.getElementById('hero-rank');
    
    if (heroRank) {
        let rankName = 'Principiante';
        
        if (rating >= 2700) {
            rankName = 'Super Gran Maestro';
        } else if (rating >= 2500) {
            rankName = 'Gran Maestro';
        } else if (rating >= 2400) {
            rankName = 'Maestro Internazionale';
        } else if (rating >= 2200) {
            rankName = 'Maestro';
        } else if (rating >= 2000) {
            rankName = 'Candidato Maestro';
        } else if (rating >= 1800) {
            rankName = 'Dilettante A';
        } else if (rating >= 1600) {
            rankName = 'Dilettante B';
        } else if (rating >= 1400) {
            rankName = 'Dilettante C';
        } else if (rating >= 1200) {
            rankName = 'Dilettante D';
        }
        
        heroRank.textContent = rankName;
    }
    
    // Imposta la bandiera della nazionalità
    const heroFlag = document.getElementById('hero-flag');
    if (heroFlag) {
        const country = user.country || 'it'; // Default Italia
        heroFlag.className = `flag-icon flag-icon-${country}`;
        heroFlag.title = getCountryName(country);
    }
    
    // Carica l'avatar dell'utente
    loadUserAvatar(user);
}

/**
 * Carica l'avatar dell'utente nella hero section
 */
function loadUserAvatar(user) {
    const avatarContainer = document.querySelector('.skm-user-avatar');
    if (!avatarContainer) return;
    
    // Calcola il livello in base al rating per determinare l'avatar
    const rating = user.rating || 1000;
    let avatarLevel = 'Principiante';
    
    if (rating >= 2700) {
        avatarLevel = 'Super Gran Maestro';
    } else if (rating >= 2500) {
        avatarLevel = 'Gran Maestro';
    } else if (rating >= 2400) {
        avatarLevel = 'Maestro Internazionale';
    } else if (rating >= 2200) {
        avatarLevel = 'Maestro';
    } else if (rating >= 2000) {
        avatarLevel = 'Candidato Maestro';
    } else if (rating >= 1800) {
        avatarLevel = 'Dilettante A';
    } else if (rating >= 1600) {
        avatarLevel = 'Dilettante B';
    } else if (rating >= 1400) {
        avatarLevel = 'Dilettante C';
    } else if (rating >= 1200) {
        avatarLevel = 'Dilettante D';
    }
    
    // Crea l'immagine dell'avatar
    avatarContainer.innerHTML = '';
    
    const avatar = document.createElement('img');
    avatar.src = `img/avatar/${avatarLevel.replace(' ', '%20')}.webp`;
    avatar.alt = `Avatar ${avatarLevel}`;
    avatar.className = 'skm-avatar-img';
    avatar.onerror = function() {
        // Fallback se l'immagine webp non è supportata
        this.src = `img/avatar/${avatarLevel.replace(' ', '%20')}.png`;
    };
    
    avatarContainer.appendChild(avatar);
}

/**
 * Ottiene il nome del paese dal codice ISO
 */
function getCountryName(countryCode) {
    const countries = {
        'it': 'Italia',
        'us': 'Stati Uniti',
        'gb': 'Regno Unito',
        'fr': 'Francia',
        'de': 'Germania',
        'es': 'Spagna',
        'jp': 'Giappone',
        'cn': 'Cina',
        'br': 'Brasile',
        'ar': 'Argentina',
        // Aggiungi altri paesi se necessario
    };
    
    return countries[countryCode] || 'Sconosciuto';
}

/**
 * Configura event listener per i pulsanti e le interazioni
 */
function setupEventListeners() {
    // Gestione del pulsante di gioco rapido
    const playOnlineButton = document.getElementById('play-online-button');
    if (playOnlineButton) {
        playOnlineButton.addEventListener('click', function() {
            // Avvia la ricerca di una partita online
            startMatchmaking();
        });
    }
    
    // Gestione del pulsante di nuova partita
    const newGameButton = document.getElementById('new-game-button');
    if (newGameButton) {
        // Rimuove tutti i listener esistenti clonando il pulsante
        const clonedButton = newGameButton.cloneNode(true);
        newGameButton.parentNode.replaceChild(clonedButton, newGameButton);
        
        // Aggiungi il nuovo listener
        clonedButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Verifica se l'utente è autenticato
            if (!localStorage.getItem('loggedInUser') && (!window.authUtils || !window.authUtils.isLoggedIn())) {
                // Mostra il popup di login
                const loginPopup = document.getElementById('login-popup');
                if (loginPopup) {
                    loginPopup.style.display = 'flex';
                }
                return;
            }
            
            // Prima di mostrare il modal, verifica che il socket sia connesso
            // NON reinizializzare il socket se è già connesso per evitare di perdere la coda
            if (!window.socket || !window.socket.connected) {
                if (typeof initializeSocket === 'function') {
                    console.log('[MATCHMAKING] Socket non connesso, inizializzazione...');
                    console.log('[MATCHMAKING] Token presente:', localStorage.getItem('token') ? 'YES' : 'NO');
                    initializeSocket();
                }
            } else {
                console.log('[MATCHMAKING] Socket già connesso, uso quello esistente');
                console.log('[MATCHMAKING] Socket auth:', window.socket.auth);
                
                // Verifica che il socket abbia il token di autenticazione
                const token = localStorage.getItem('token');
                if (token && (!window.socket.auth || !window.socket.auth.token)) {
                    console.log('[MATCHMAKING] Socket senza token, aggiornamento auth...');
                    window.socket.auth = { token: token };
                    // Forza la riconnessione con il nuovo token
                    window.socket.disconnect();
                    window.socket.connect();
                }
            }
            
            // Mostra il modal di matchmaking
            const matchmakingModal = document.getElementById('matchmaking-modal');
            if (matchmakingModal) {
                matchmakingModal.style.display = 'flex';
                
                // Attendi un momento per la connessione del socket
                setTimeout(() => {
                    // Avvia il matchmaking solo senza navigazione
                    if (window.socket && window.socket.connected) {
                        console.log('[MATCHMAKING] Socket connesso, invio findMatch');
                        console.log('[MATCHMAKING] Token presente:', localStorage.getItem('token') ? 'YES' : 'NO');
                        
                        // Rimuovi eventuali handler precedenti e aggiungi il nuovo
                        window.socket.off('matchFound');
                        window.socket.on('matchFound', (data) => {
                            console.log('[MATCHMAKING] Match trovato!', data);
                            
                            // Chiudi il modal
                            matchmakingModal.style.display = 'none';
                            
                            // Mostra il messaggio di successo
                            console.log('[MATCHMAKING] Partita trovata con', data.opponent ? data.opponent.name : 'un avversario');
                            
                            // Salva i dati della partita per il client
                            if (data.gameId) {
                                console.log('[MATCHMAKING] Salvando dati partita con gameId:', data.gameId);
                                console.log('[MATCHMAKING] Dati completi:', data);
                                sessionStorage.setItem('gameData', JSON.stringify(data));
                                
                                // Verifica che i dati siano stati salvati correttamente
                                const savedData = sessionStorage.getItem('gameData');
                                console.log('[MATCHMAKING] Dati salvati in sessionStorage:', savedData);
                                
                                // Naviga alla pagina di gioco dopo un breve ritardo
                                setTimeout(() => {
                                    console.log('[MATCHMAKING] Navigando a /game con gameId:', data.gameId);
                                    // Il gameId è già salvato in sessionStorage dal codice sopra
                                    window.location.href = '/game';
                                }, 1000);
                            } else {
                                console.error('[MATCHMAKING] Nessun gameId ricevuto nei dati del match');
                            }
                        });
                        
                        const userData = window.authUtils ? window.authUtils.getCurrentUser() : null;
                        const requestData = {
                            rating: userData?.rating || 1000,
                            username: userData?.username || 'Giocatore'
                        };
                        
                        console.log('[MATCHMAKING] Invio findMatch con dati:', requestData);
                        console.log('[MATCHMAKING] Socket ID:', window.socket.id);
                        console.log('[MATCHMAKING] Socket auth attuale:', window.socket.auth);
                        
                        window.socket.emit('findMatch', requestData);
                    } else {
                        console.error('[MATCHMAKING] Socket non connesso dopo reinizializzazione');
                        matchmakingModal.style.display = 'none';
                        showError('Errore di connessione al server. Ricarica la pagina.');
                    }
                }, 500); // Ritardo per dare tempo al socket di connettersi
            }
        });
    }
    
    // Gestione del pulsante di cancellazione del matchmaking
    const cancelMatchmakingButton = document.getElementById('cancel-matchmaking');
    if (cancelMatchmakingButton) {
        cancelMatchmakingButton.addEventListener('click', function() {
            const matchmakingModal = document.getElementById('matchmaking-modal');
            if (matchmakingModal) {
                matchmakingModal.style.display = 'none';
            }
            
            // Cancella la ricerca se il socket è connesso
            if (window.socket && window.socket.connected) {
                window.socket.emit('cancelMatchmaking');
            }
        });
    }
    
    // Gestione del pulsante di allenamento
    const trainingButton = document.getElementById('training-button');
    if (trainingButton) {
        trainingButton.addEventListener('click', function() {
            // Avvia modalità allenamento contro AI
            startTrainingMode();
        });
    }
    
    // Gestione del pulsante di sfida locale
    const startLocalButton = document.getElementById('start-local-button');
    if (startLocalButton) {
        startLocalButton.addEventListener('click', function() {
            console.log('Click su sfida un amico');
            
            // Usa la funzione esistente del gioco per mostrare la schermata nomi
            if (typeof showPlayerNamesScreen === 'function') {
                showPlayerNamesScreen();
            } else {
                console.error('La funzione showPlayerNamesScreen non è disponibile');
            }
        });
    }
    
    // Gestione del pulsante logout
    const logoutButton = document.getElementById('logout-button');
    if (logoutButton) {
        logoutButton.addEventListener('click', async function() {
            // Disabilita il pulsante per evitare doppi clic
            logoutButton.disabled = true;
            
            // Effettua logout
            if (window.authUtils && window.authUtils.logout) {
                try {
                    // Attendi che il logout venga completato
                    await window.authUtils.logout();
                    
                    // Aggiungi un piccolo ritardo per assicurarsi che tutto sia pulito
                    setTimeout(() => {
                        // Reindirizza alla home dopo logout
                        window.location.href = '/';
                    }, 100);
                } catch (error) {
                    console.error('Errore durante il logout:', error);
                    // Riabilita il pulsante in caso di errore
                    logoutButton.disabled = false;
                }
            }
        });
    }
    
    // Gestione del link alla classifica nella hero
    const heroLeaderboardLink = document.getElementById('hero-leaderboard-link');
    if (heroLeaderboardLink) {
        heroLeaderboardLink.addEventListener('click', function(e) {
            e.preventDefault();
            showLeaderboard();
        });
    }
    
    // Gestione del link alla classifica nella sidebar
    const classificaLink = document.getElementById('classifica-link');
    if (classificaLink) {
        classificaLink.addEventListener('click', function() {
            showLeaderboard();
        });
    }
    
    // Gestione tabs cronologia/statistiche
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            
            // Rimuovi active da tutti i tab
            tabButtons.forEach(btn => btn.classList.remove('active'));
            // Aggiungi active al tab cliccato
            this.classList.add('active');
            
            // Nascondi tutti i contenuti
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            
            // Mostra il contenuto selezionato
            const selectedTab = document.getElementById(`${tabName}-tab`);
            if (selectedTab) {
                selectedTab.style.display = 'block';
                
                // Carica i dati specifici per ogni tab
                if (tabName === 'stats') {
                    loadStatistics();
                } else if (tabName === 'history') {
                    loadMatchHistory();
                }
            }
        });
    });
    
    // Gestione delle icone azione nella hero
    const heroFriendsLink = document.getElementById('hero-friends-link');
    if (heroFriendsLink) {
        heroFriendsLink.addEventListener('click', function(e) {
            e.preventDefault();
            showFriendsSection();
        });
    }
    
    const heroMessagesLink = document.getElementById('hero-messages-link');
    if (heroMessagesLink) {
        heroMessagesLink.addEventListener('click', function(e) {
            e.preventDefault();
            showMessagesSection();
        });
    }
    
    const heroSettingsLink = document.getElementById('hero-settings-link');
    if (heroSettingsLink) {
        heroSettingsLink.addEventListener('click', function(e) {
            e.preventDefault();
            showSettingsSection();
        });
    }
    
    // NON aggiungiamo listener ai pulsanti della schermata nomi perché sono già gestiti dal gioco originale
    // Il confirm-names-button e back-to-menu-button hanno già i loro listener in script.js
}

/**
 * Mostra la schermata della classifica
 */
function showLeaderboard() {
    // Nascondi la home
    const mainContent = document.querySelector('.skemino-main-content');
    if (mainContent) {
        mainContent.style.display = 'none';
    }
    
    // Mostra la classifica
    const classificaScreen = document.getElementById('classifica-screen');
    if (classificaScreen) {
        classificaScreen.style.display = 'block';
        
        // Carica i dati della classifica se esiste la funzione
        if (typeof loadClassifica === 'function') {
            loadClassifica();
        }
    }
}

/**
 * Mostra la sezione amici
 */
function showFriendsSection() {
    console.log('Mostra sezione amici');
    // TODO: Implementare la sezione amici
    alert('Sezione amici in arrivo!');
}

/**
 * Mostra la sezione messaggi
 */
function showMessagesSection() {
    console.log('Mostra sezione messaggi');
    // TODO: Implementare la sezione messaggi
    alert('Sezione messaggi in arrivo!');
}

/**
 * Mostra la sezione impostazioni
 */
function showSettingsSection() {
    console.log('Mostra sezione impostazioni');
    // TODO: Implementare la sezione impostazioni
    alert('Sezione impostazioni in arrivo!');
}

/**
 * Carica e visualizza le partite giornaliere
 */
function loadDailyMatches() {
    const dailyMatchesContent = document.getElementById('daily-matches-content');
    if (!dailyMatchesContent) return;
    
    // Mostra il loader
    const loadingDiv = dailyMatchesContent.querySelector('.daily-matches-loading');
    const listDiv = document.getElementById('daily-matches-list');
    const noMatchesDiv = document.getElementById('no-matches-message');
    
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (listDiv) listDiv.style.display = 'none';
    if (noMatchesDiv) noMatchesDiv.style.display = 'none';
    
    // TODO: sostituire con chiamata API reale
    // Simula il caricamento delle partite (per ora con dati fittizi)
    setTimeout(() => {
        // Nascondi il loader
        if (loadingDiv) loadingDiv.style.display = 'none';
        
        // Per ora mostriamo dati di esempio
        const mockMatches = [
            {
                id: 1,
                opponentName: 'Giocatore123',
                result: 'vittoria',
                score: '45 - 38',
                time: '14:32',
                duration: '12 min'
            },
            {
                id: 2,
                opponentName: 'ProPlayer',
                result: 'sconfitta',
                score: '42 - 48',
                time: '11:20',
                duration: '15 min'
            },
            {
                id: 3,
                opponentName: 'Strategist',
                result: 'vittoria',
                score: '50 - 44',
                time: '09:45',
                duration: '18 min'
            }
        ];
        
        if (loadingDiv) loadingDiv.style.display = 'none';
        
        if (mockMatches.length > 0) {
            // Mostra la lista delle partite
            if (listDiv) {
                listDiv.innerHTML = `
                    <div class="daily-matches-grid">
                        ${mockMatches.map(match => `
                            <div class="daily-match-card ${match.result}">
                                <div class="match-header">
                                    <span class="match-time">${match.time}</span>
                                    <span class="match-duration">${match.duration}</span>
                                </div>
                                <div class="match-body">
                                    <div class="match-players">
                                        <span class="player-name">Tu</span>
                                        <span class="vs">VS</span>
                                        <span class="opponent-name">${match.opponentName}</span>
                                    </div>
                                    <div class="match-score">${match.score}</div>
                                    <div class="match-result ${match.result}">
                                        ${match.result === 'vittoria' ? '<i class="fas fa-trophy"></i> Vittoria' : '<i class="fas fa-times-circle"></i> Sconfitta'}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
                listDiv.style.display = 'block';
            }
        } else {
            // Mostra il messaggio "nessuna partita"
            if (noMatchesDiv) noMatchesDiv.style.display = 'block';
        }
    }, 1500);
}

/**
 * Carica e visualizza la cronologia delle partite
 */
function loadMatchHistory() {
    const historyContainer = document.getElementById('match-history-container');
    if (!historyContainer) return;
    
    // Mostra il loader
    const loadingDiv = historyContainer.querySelector('.match-history-loading');
    const listDiv = document.getElementById('match-history-list');
    const noHistoryDiv = document.getElementById('no-history-message');
    
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (listDiv) listDiv.style.display = 'none';
    if (noHistoryDiv) noHistoryDiv.style.display = 'none';
    
    // TODO: sostituire con chiamata API reale
    // Simula il caricamento della cronologia (per ora con dati fittizi)
    setTimeout(() => {
        // Nascondi il loader
        if (loadingDiv) loadingDiv.style.display = 'none';
        
        // Dati di esempio per la cronologia
        const mockHistory = [
            {
                id: 10,
                date: '15/05/2024',
                time: '18:45',
                opponentName: 'ProPlayer',
                result: 'vittoria',
                score: '48 - 42',
                duration: '16 min'
            },
            {
                id: 9,
                date: '15/05/2024',
                time: '14:20',
                opponentName: 'Strategist',
                result: 'sconfitta',
                score: '44 - 50',
                duration: '18 min'
            },
            {
                id: 8,
                date: '14/05/2024',
                time: '21:10',
                opponentName: 'GamerX',
                result: 'vittoria',
                score: '46 - 41',
                duration: '14 min'
            },
            {
                id: 7,
                date: '14/05/2024',
                time: '16:30',
                opponentName: 'Maestro99',
                result: 'vittoria',
                score: '51 - 45',
                duration: '20 min'
            },
            {
                id: 6,
                date: '13/05/2024',
                time: '19:15',
                opponentName: 'ChampionPlayer',
                result: 'sconfitta',
                score: '39 - 45',
                duration: '12 min'
            },
            {
                id: 5,
                date: '12/05/2024',
                time: '20:00',
                opponentName: 'NewPlayer',
                result: 'vittoria',
                score: '55 - 38',
                duration: '15 min'
            }
        ];
        
        if (loadingDiv) loadingDiv.style.display = 'none';
        
        if (mockHistory.length > 0) {
            // Mostra la tabella della cronologia
            if (listDiv) {
                const tableDiv = listDiv.querySelector('.match-history-table');
                if (tableDiv) {
                    tableDiv.innerHTML = `
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Ora</th>
                                    <th>Avversario</th>
                                    <th>Risultato</th>
                                    <th>Punteggio</th>
                                    <th>Durata</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${mockHistory.map(match => `
                                    <tr class="history-row ${match.result}">
                                        <td>${match.date}</td>
                                        <td>${match.time}</td>
                                        <td class="opponent-name">${match.opponentName}</td>
                                        <td class="match-result">
                                            <span class="result-badge ${match.result}">
                                                ${match.result === 'vittoria' ? 
                                                    '<i class="fas fa-trophy"></i> Vittoria' : 
                                                    '<i class="fas fa-times-circle"></i> Sconfitta'}
                                            </span>
                                        </td>
                                        <td class="match-score">${match.score}</td>
                                        <td>${match.duration}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;
                }
                listDiv.style.display = 'block';
            }
        } else {
            // Mostra il messaggio "nessuna cronologia"
            if (noHistoryDiv) noHistoryDiv.style.display = 'block';
        }
    }, 1500);
}

/**
 * Carica e visualizza le statistiche
 */
function loadStatistics() {
    const statsContainer = document.getElementById('stats-container');
    if (!statsContainer) return;
    
    // Mostra il loader
    const loadingDiv = statsContainer.querySelector('.stats-loading');
    const contentDiv = document.getElementById('stats-content');
    const noStatsDiv = document.getElementById('no-stats-message');
    
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (contentDiv) contentDiv.style.display = 'none';
    if (noStatsDiv) noStatsDiv.style.display = 'none';
    
    // Simula il caricamento delle statistiche
    setTimeout(() => {
        // Dati di esempio per le statistiche
        const mockStats = {
            totalGames: 85,
            wins: 54,
            losses: 31,
            winRate: 63.5,
            avgScore: 46.2,
            avgDuration: '15 min',
            longestWinStreak: 8,
            currentStreak: 3,
            mostPlayedOpponent: 'ProPlayer',
            favoriteCards: ['P7', 'C10', 'F12'],
            bestScore: 68,
            worstScore: 32
        };
        
        if (loadingDiv) loadingDiv.style.display = 'none';
        
        if (mockStats.totalGames > 0) {
            // Mostra le statistiche
            if (contentDiv) {
                contentDiv.innerHTML = `
                    <div class="stats-two-column-layout">
                        <div class="stats-left-column">
                            <div class="stat-card primary">
                                <div class="stat-value">${mockStats.winRate}%</div>
                                <div class="stat-label">Percentuale di vittoria</div>
                                <div class="stat-details">${mockStats.wins}V - ${mockStats.losses}S</div>
                            </div>
                            
                            <div class="stats-mini-grid">
                                <div class="stat-card">
                                    <div class="stat-value">${mockStats.totalGames}</div>
                                    <div class="stat-label">Partite totali</div>
                                </div>
                                
                                <div class="stat-card">
                                    <div class="stat-value">${mockStats.avgScore}</div>
                                    <div class="stat-label">Punteggio medio</div>
                                </div>
                                
                                <div class="stat-card">
                                    <div class="stat-value">${mockStats.avgDuration}</div>
                                    <div class="stat-label">Durata media</div>
                                </div>
                            </div>
                            
                            <div class="stat-box">
                                <h4><i class="fas fa-fire"></i> Serie di vittorie</h4>
                                <p>Migliore: ${mockStats.longestWinStreak} partite</p>
                                <p>Attuale: ${mockStats.currentStreak} partite</p>
                            </div>
                        </div>
                        
                        <div class="stats-right-column">
                            <div class="stat-box">
                                <h4><i class="fas fa-star"></i> Record personali</h4>
                                <p>Punteggio più alto: ${mockStats.bestScore}</p>
                                <p>Punteggio più basso: ${mockStats.worstScore}</p>
                            </div>
                            
                            <div class="stat-box">
                                <h4><i class="fas fa-user"></i> Avversario più frequente</h4>
                                <p>${mockStats.mostPlayedOpponent}</p>
                            </div>
                            
                            <div class="favorite-cards">
                                <h4><i class="fas fa-heart"></i> Carte preferite</h4>
                                <div class="cards-list">
                                    ${mockStats.favoriteCards.map(card => `
                                        <div class="fav-card">
                                            <img src="img/carte/${card}.webp" alt="${card}" />
                                            <span>${card}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                contentDiv.style.display = 'block';
            }
        } else {
            // Mostra il messaggio "nessuna statistica"
            if (noStatsDiv) noStatsDiv.style.display = 'block';
        }
    }, 1500);
}