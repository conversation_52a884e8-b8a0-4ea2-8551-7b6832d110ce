<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=yes">
    <meta name="description" content="Skèmino - Il gioco strategico che unisce carte e strategia. Gioca online o in locale contro amici e sfida giocatori da tutto il mondo.">
    <meta name="keywords" content="Skèmino, gioco di carte, gioco strategico, gioco online, gioco da tavolo">
    <meta name="theme-color" content="#172a45">
    <meta name="author" content="Skemino Development Team">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://skemino.it/">
    <meta property="og:title" content="Skèmino - Il gioco strategico di carte e strategia">
    <meta property="og:description" content="Gioca a Skèmino online o in locale. Sfida amici e avversari in questo innovativo gioco strategico.">
    <meta property="og:image" content="img/carte/skemino.webp">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://skemino.it/">
    <meta property="twitter:title" content="Skèmino - Il gioco strategico di carte e strategia">
    <meta property="twitter:description" content="Gioca a Skèmino online o in locale. Sfida amici e avversari in questo innovativo gioco strategico.">
    <meta property="twitter:image" content="img/carte/skemino.webp">

    <link rel="canonical" href="https://skemino.it/">
    <title>Skèmino - Il gioco strategico di carte e strategia</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/multiplayer.css">
    <link rel="stylesheet" href="css/card-animations.css">
    <link rel="stylesheet" href="css/psn-unified.css">
    <link rel="stylesheet" href="css/board-snapshot.css">
    <link rel="stylesheet" href="css/index.css">
    <link rel="stylesheet" href="css/index_home.css">
    <link rel="stylesheet" href="css/page-transitions.css">
    <link rel="stylesheet" href="css/hand-slots.css">
    <link rel="stylesheet" href="css/mobile-menu.css">
    <link rel="stylesheet" href="css/online-interface.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Precaricamento delle immagini critiche -->
    <link rel="preload" href="img/carte/Loop_card.webp" as="image" type="image/webp" fetchpriority="low">
    <link rel="preload" href="img/carte/card-back.webp" as="image" type="image/webp" fetchpriority="high">
    <link rel="preload" href="img/carte/skemino.webp" as="image" type="image/webp" fetchpriority="high">

    <!-- Audio preload rimosso per evitare errori nel browser -->
</head>
<body class="loading">
    <!-- Precarica le immagini di copertura delle carte per evitare il flash durante l'animazione -->
    <div class="preload-card-backs" aria-hidden="true">
        <!-- Immagini precaricate con attributi ottimizzati -->
        <img src="img/carte/card-back.webp" alt="Card Back" class="card-back-image card-visible"
             loading="eager" fetchpriority="high" decoding="sync"
             draggable="false" style="transform: translateZ(0); -webkit-transform: translateZ(0);">

        <img src="/img/Cover carte/cover.png" alt="Card Back" class="card-back-image card-visible"
             loading="eager" fetchpriority="high" decoding="sync"
             draggable="false" style="transform: translateZ(0); -webkit-transform: translateZ(0);">
    </div>

    <div id="homepage">
        <!-- Hamburger Menu Button for Mobile/Tablet -->
        <div class="mobile-header">
            <div class="mobile-logo">
                <img src="img/carte/skemino.webp" alt="Skèmino" class="logo-image home-link" title="Torna alla home">
            </div>
            <button class="hamburger-menu">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Mobile Menu Overlay -->
        <div class="mobile-menu-overlay">
            <div class="mobile-menu">
                <div class="mobile-menu-header">
                    <div class="mobile-logo">
                        <img src="img/carte/skemino.webp" alt="Skèmino" class="logo-image home-link" title="Torna alla home">
                    </div>
                    <button class="close-menu">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <nav class="mobile-nav">
                    <ul>
                        <li class="active"><i class="fas fa-play"></i> Gioca</li>
                        <li><i class="fas fa-book"></i> Regole</li>
                        <li><i class="fas fa-trophy"></i> Tornei</li>
                        <li><i class="fas fa-graduation-cap"></i> Tutorial</li>
                        <li><i class="fas fa-chart-line"></i> Statistiche</li>
                        <li><i class="fas fa-users"></i> Community</li>
                        <li id="mobile-classifica-link"><i class="fas fa-medal"></i> Classifica</li>
                    </ul>
                </nav>
                <div class="mobile-menu-footer">
                    <div id="mobile-auth-buttons">
                        <button id="mobile-login-button" class="auth-btn"><i class="fas fa-sign-in-alt"></i> Accedi</button>
                        <button id="mobile-register-button" class="auth-btn"><i class="fas fa-user-plus"></i> Registrati</button>
                    </div>
                    <div id="mobile-user-profile" style="display: none;">
                        <div class="user-info">
                            <span id="mobile-username-display"></span>
                            <span id="mobile-user-rank"></span>
                        </div>
                        <button id="mobile-logout-button" class="auth-btn"><i class="fas fa-sign-out-alt"></i> Esci</button>
                        <button id="mobile-fix-auth-button" class="auth-btn" style="display: none; margin-top: 5px; background-color: #e74c3c;"><i class="fas fa-wrench"></i> Ripara Accesso</button>
                    </div>
                    <div class="mobile-controls">
                        <button class="lang-switch"><i class="fas fa-globe"></i> Italiano</button>
                        <button class="support-btn"><i class="fas fa-question-circle"></i> Supporto</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <!-- Menu laterale sinistro fisso -->
            <div class="skemino-sidebar">
                <div class="skemino-logo-home">
                    <img src="img/carte/skemino.webp" alt="Skèmino" class="skemino-logo-image skemino-home-link" title="Torna alla home">
                </div>
                <nav>
                    <ul>
                        <li class="skemino-active"><i class="fas fa-play"></i> Gioca</li>
                        <li><i class="fas fa-book"></i> Regole</li>
                        <li><i class="fas fa-trophy"></i> Tornei</li>
                        <li><i class="fas fa-graduation-cap"></i> Tutorial</li>
                        <li><i class="fas fa-chart-line"></i> Statistiche</li>
                        <li><i class="fas fa-users"></i> Community</li>
                        <li id="classifica-link"><i class="fas fa-medal"></i> Classifica</li>
                    </ul>
                </nav>
                <div class="skemino-sidebar-footer">
                    <div id="auth-buttons">
                        <button id="login-button" class="skemino-auth-btn"><i class="fas fa-sign-in-alt"></i> Accedi</button>
                        <button id="register-button" class="skemino-auth-btn"><i class="fas fa-user-plus"></i> Registrati</button>
                    </div>
                    <div id="user-profile" style="display: none;">
                        <div class="skemino-user-info">
                            <span id="username-display"></span>
                            <span id="user-rank"></span>
                        </div>
                        <button id="logout-button" class="skemino-auth-btn"><i class="fas fa-sign-out-alt"></i> Esci</button>
                    </div>
                    <div class="skemino-sidebar-controls">
                        <button class="skemino-lang-switch"><i class="fas fa-globe"></i> Italiano</button>
                        <button class="skemino-support-btn"><i class="fas fa-question-circle"></i> Supporto</button>
                    </div>
                </div>
            </div>
            
            <!-- Contenuto principale -->
            <div class="skemino-main-wrapper">
                <main class="skemino-main-content">
                    <!-- Sezione Hero con tabellone e pulsanti -->
                    <section class="skemino-hero-section">
                        <div class="skemino-board-preview">
                            <div class="skemino-tabellone-image">
                                <img src="img/tabellone/tabellone.webp" alt="Tabellone Skèmino" class="skemino-tabellone-img">
                            </div>
                        </div>
                        <div class="skemino-hero-content">
                            <div class="skemino-hero-title-container">
                                <div id="guest-title-view">
                                    <h1>Gioca a Skèmino Online</h1>
                                    <h2>La nuova frontiera dei giochi di strategia!</h2>
                                </div>
                                <div id="logged-user-title-view" style="display: none;">
                                    <div class="skemino-user-hero-header">
                                        <div class="skemino-user-hero-avatar">
                                            <!-- L'avatar verrà inserito qui da JavaScript -->
                                        </div>
                                        <div class="skemino-user-hero-info">
                                            <h1>Benvenuto, <span id="hero-username">Giocatore</span>!</h1>
                                            <h2>
                                                <span id="hero-rank">Principiante</span>
                                                <span id="hero-flag" class="skemino-country-flag"></span>
                                            </h2>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="skemino-hero-actions-container">
                                <div class="skemino-play-buttons">
                                    <a href="play" id="quick-game-button" class="skemino-primary-btn skemino-online-btn">
                                        <i class="fas fa-globe"></i>
                                        <span class="skemino-button-content">
                                            <span class="skemino-primary-text">Gioca Online</span>
                                            <span class="skemino-secondary-text">Sfida giocatori da tutto il mondo</span>
                                        </span>
                                    </a>
                                    <button id="training-button" class="skemino-primary-btn skemino-training-btn">
                                        <i class="fas fa-brain"></i>
                                        <span class="skemino-button-content">
                                            <span class="skemino-primary-text">Allenamento</span>
                                            <span class="skemino-secondary-text">Migliora le tue abilità offline</span>
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Sezione Regole -->
                    <section class="skemino-rules-section">
                        <div class="skemino-section-header">
                            <h2>Come si Gioca a Skèmino</h2>
                            <p>Una combinazione unica di carte e strategia</p>
                        </div>
                        <div class="skemino-rules-content">
                            <div class="skemino-rules-video">
                                <iframe width="560" height="315" src="https://www.youtube.com/embed/_923TbW3E-g" title="Skèmino Gameplay" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>
                            </div>
                            <div class="skemino-rules-text">
                                <blockquote>
                                    "Il gioco ha avuto successo in famiglia ed è un qualcosa di diverso rispetto ai soliti giochi di società.
                                    All'interno della scatola vengono fornite delle chiare istruzioni (ovviamente oltre a tutti gli elementi per poter giocare).
                                    Dopo qualche partita il regolamento è stato più chiaro e ha lasciato spazio al divertimento.
                                    Assolutamente consigliato."
                                    <cite>— Amazon Vine</cite>
                                </blockquote>
                                <p><strong>Obiettivo del gioco</strong><br>
                                Essere il primo a posizionare l'ultima carta su un vertice controllato. Ma attenzione: la strada verso la vittoria richiede strategia e intuizione!</p>
                                <button class="skemino-learn-more-btn">Scopri le Regole Complete</button>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Sezione Academy -->
                    <section class="skemino-academy-section">
                        <div class="skemino-academy-header">
                            <h2 class="skemino-academy-title">Academy Skèmino</h2>
                            <p class="skemino-academy-subtitle">Articoli e guide per migliorare il tuo gioco</p>
                        </div>
                        <div class="skemino-features-section">
                            <a href="#academy-strategia" class="skemino-feature-card">
                                <i class="fas fa-brain"></i>
                                <h3>Strategie Avanzate</h3>
                                <p>Scopri le migliori tattiche per dominare il tabellone</p>
                                <span class="skemino-read-more">Leggi l'articolo →</span>
                            </a>
                            <a href="#academy-principianti" class="skemino-feature-card">
                                <i class="fas fa-graduation-cap"></i>
                                <h3>Guida per Principianti</h3>
                                <p>Impara le basi e i consigli per iniziare alla grande</p>
                                <span class="skemino-read-more">Leggi l'articolo →</span>
                            </a>
                            <a href="#academy-tornei" class="skemino-feature-card">
                                <i class="fas fa-trophy"></i>
                                <h3>Tornei e Competizioni</h3>
                                <p>Tutto quello che devi sapere per partecipare ai tornei ufficiali</p>
                                <span class="skemino-read-more">Leggi l'articolo →</span>
                            </a>
                        </div>
                    </section>
                </main>
            </div>
        </div>

    <!-- Elementi originali del gioco, nascosti inizialmente -->
    <div id="main-menu" style="display: none;"></div>

    <!-- Schermata inserimento nomi giocatori -->
    <div id="player-names-screen" style="display: none;">
        <div class="names-container">
            <h2>Inserisci i nomi dei giocatori</h2>
            <p class="names-info">Il Giocatore G1 (bianco) inizia sempre la partita. Il dado colore determinerà quale giocatore sarà G1.</p>
            <div class="player-name-input">
                <label for="player1-name">
                    <i class="fas fa-user"></i> Giocatore 1
                </label>
                <input type="text" id="player1-name" maxlength="20" placeholder="Nome Giocatore 1">
            </div>
            <div class="player-name-input">
                <label for="player2-name">
                    <i class="fas fa-user"></i> Giocatore 2
                </label>
                <input type="text" id="player2-name" maxlength="20" placeholder="Nome Giocatore 2">
            </div>
            <p id="names-error-message" class="error-message"></p>
            <div class="names-buttons">
                <button id="confirm-names-button" class="primary-btn">
                    <i class="fas fa-check"></i> Conferma
                </button>
                <button id="back-to-menu-button" class="secondary-btn">
                    <i class="fas fa-arrow-left"></i> Indietro
                </button>
            </div>
        </div>
    </div>

    <div id="setup-animation" style="display: none;">
        <h2>Preparazione Partita...</h2>
        <div id="dice-area">
            <!-- I dadi appariranno qui -->
        </div>
        <p id="dice-result-text">Lancio dei dadi...</p>
    </div>

    <!-- Riquadro di vittoria -->
    <div id="victory-screen" style="display: none;">
        <div class="victory-container">
            <div class="victory-header">
                <i class="fas fa-trophy victory-icon"></i>
                <h2>Vittoria!</h2>
            </div>
            <div class="victory-content">
                <p id="winner-name">Nome Vincitore</p>
                <div id="rating-changes">
                    <p id="winner-rating" class="rating-change winner-rating">Rating: <span class="old-rating"></span> → <span class="new-rating"></span> <span class="rating-diff"></span></p>
                    <p id="loser-rating" class="rating-change loser-rating">Rating: <span class="old-rating"></span> → <span class="new-rating"></span> <span class="rating-diff"></span></p>
                </div>
                <p id="victory-reason">Motivo della vittoria</p>
            </div>
            <div class="victory-buttons">
                <button id="examine-game-victory" class="primary-btn">
                    <i class="fas fa-search"></i> Esamina Partita
                </button>
                <button id="new-game-victory" class="secondary-btn">
                    <i class="fas fa-redo"></i> Nuova Partita
                </button>
            </div>
        </div>
    </div>

    <!-- Overlay per l'animazione di inizio battaglia -->
    <div id="battle-start-overlay">
        <div class="battle-start-text"></div>
        <div class="battle-particles"></div>
    </div>

    <!-- Matchmaking Modal -->
    <div id="matchmaking-modal" class="matchmaking-modal">
        <div class="matchmaking-content">
            <h2>Ricerca Avversario</h2>
            <div class="matchmaking-spinner"></div>
            <p id="matchmaking-status" class="matchmaking-status">Ricerca di un avversario in corso...</p>
            <div class="matchmaking-buttons">
                <button id="cancel-matchmaking" class="cancel-matchmaking"><i class="fas fa-times-circle"></i> Annulla Ricerca</button>
            </div>
        </div>
    </div>



    <!-- Turn Timer -->
    <div id="turn-timer-container" class="turn-timer-container" style="display: none;">
        <div id="turn-timer" class="turn-timer">00:00</div>
    </div>

    <!-- Notifications -->
    <div id="notification-container" class="notification-container"></div>

    <!-- Login Popup -->
    <div id="login-popup" class="game-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h4><i class="fas fa-sign-in-alt"></i> Accedi</h4>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="popup-login-username"><i class="fas fa-user"></i> Username</label>
                    <input type="text" id="popup-login-username" placeholder="Inserisci il tuo username" required>
                </div>
                <div class="form-group">
                    <label for="popup-login-password"><i class="fas fa-lock"></i> Password</label>
                    <input type="password" id="popup-login-password" placeholder="Inserisci la tua password" required>
                </div>
                <p id="login-error-message" class="error-message"></p>
                <div class="remember-forgot">
                    <div class="remember-me">
                        <input type="checkbox" id="popup-remember-me">
                        <label for="popup-remember-me">Ricordami</label>
                    </div>
                    <a href="#" id="popup-forgot-password">Password dimenticata?</a>
                </div>

                <div class="social-login-divider">
                    <span>oppure</span>
                </div>

                <div class="social-login-buttons">
                    <button type="button" id="google-login-btn" class="social-login-btn google-btn">
                        <i class="fab fa-google"></i> Accedi con Google
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button id="popup-login-button" class="confirm-button">
                    <i class="fas fa-sign-in-alt"></i> Accedi
                </button>
                <div class="register-link">
                    Non hai un account? <a href="#" id="popup-switch-to-register">Registrati</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Registration Popup -->
    <div id="register-popup" class="game-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h4><i class="fas fa-user-plus"></i> Registrati</h4>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="popup-register-username"><i class="fas fa-user"></i> Username</label>
                    <input type="text" id="popup-register-username" placeholder="Scegli un username" required>
                </div>
                <div class="form-group">
                    <label for="popup-register-email"><i class="fas fa-envelope"></i> Email</label>
                    <input type="email" id="popup-register-email" placeholder="Inserisci la tua email" required>
                </div>
                <div class="form-group">
                    <label for="popup-register-password"><i class="fas fa-lock"></i> Password</label>
                    <input type="password" id="popup-register-password" placeholder="Crea una password" required>
                </div>
                <div class="form-group">
                    <label for="popup-register-confirm-password"><i class="fas fa-lock"></i> Conferma Password</label>
                    <input type="password" id="popup-register-confirm-password" placeholder="Conferma la tua password" required>
                </div>
                <p id="register-error-message" class="error-message"></p>
                <div class="terms-container">
                    <input type="checkbox" id="popup-terms-agreement" required>
                    <label for="popup-terms-agreement">Accetto i termini e le condizioni</label>
                </div>

                <div class="social-login-divider">
                    <span>oppure</span>
                </div>

                <div class="social-login-buttons">
                    <button type="button" id="google-register-btn" class="social-login-btn google-btn">
                        <i class="fab fa-google"></i> Registrati con Google
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button id="popup-register-button" class="confirm-button">
                    <i class="fas fa-user-plus"></i> Registrati
                </button>
                <div class="login-link">
                    Hai già un account? <a href="#" id="popup-switch-to-login">Accedi</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Classifica -->
    <div id="classifica-screen" style="display: none;">
        <div class="classifica-container">
            <div class="classifica-header">
                <h2>Classifica dei Giocatori</h2>
                <p>Ranking basato sul sistema ELO</p>
            </div>

            <div class="classifica-table-container">
                <table class="classifica-table">
                    <thead>
                        <tr>
                            <th class="position-col">Pos.</th>
                            <th class="avatar-col">Avatar</th>
                            <th class="player-col">Giocatore</th>
                            <th class="level-col">Livello</th>
                            <th class="rating-col">Rating</th>
                        </tr>
                    </thead>
                    <tbody id="classifica-body">
                        <!-- Righe della classifica generate dinamicamente da JavaScript -->
                    </tbody>
                </table>
            </div>

            <div class="classifica-legend">
                <h3>Livelli di abilità</h3>
                <div class="level-description">
                    <p>In base al rating, i giocatori sono classificati in diverse categorie:</p>
                    <ul>
                        <li><span class="level-badge principiante">P</span> Principiante (1000-1199)</li>
                        <li><span class="level-badge dilettante-d">D</span> Dilettante Categoria D (1200-1399)</li>
                        <li><span class="level-badge dilettante-c">C</span> Dilettante Categoria C (1400-1599)</li>
                        <li><span class="level-badge dilettante-b">B</span> Dilettante Categoria B (1600-1799)</li>
                        <li><span class="level-badge dilettante-a">A</span> Dilettante Categoria A (1800-1999)</li>
                        <li><span class="level-badge candidato">CM</span> Candidato Maestro (2000-2199)</li>
                        <li><span class="level-badge maestro">M</span> Maestro (2200-2399)</li>
                        <li><span class="level-badge internazionale">MI</span> Maestro Internazionale (2400-2499)</li>
                        <li><span class="level-badge gran-maestro">GM</span> Gran Maestro (2500-2699)</li>
                        <li><span class="level-badge super">SGM</span> Super Gran Maestro (2700+)</li>
                    </ul>
                </div>
            </div>

            <div class="classifica-buttons">
                <button id="back-to-menu-classifica" class="secondary-btn">
                    <i class="fas fa-arrow-left"></i> Torna al Menu
                </button>
                <button id="refresh-classifica" class="primary-btn">
                    <i class="fas fa-sync"></i> Aggiorna
                </button>
            </div>
        </div>
    </div>

    <!-- Mazzo di carte posizionato al centro assoluto dello schermo -->
    <div class="deck-area">
        <!-- Etichetta descrittiva sopra il mazzo (verrà aggiornata con il nome del giocatore 2) -->
        <div class="deck-label" id="deck-label">Mazzo di gioco</div>
        <div id="deck">
            <div class="card-stack-visual static-deck">
                <div class="card-pile-1"></div>
                <div class="card-pile-2"></div>
                <div class="card-pile-3"></div>
                <div class="card-pile-4"></div>
                <div class="card-pile-5"></div>
            </div>
        </div>
    </div>

    <div id="game-container" style="display: none;">
        <!-- Stili inline per garantire l'applicazione immediata -->
        

        <!-- Menu laterale compresso per l'interfaccia di gioco -->
        <div id="game-sidebar" class="game-sidebar">
            <div class="logo">
                <img src="img/carte/skemino.webp" alt="Skèmino" class="logo-image home-link" title="Torna alla home">
            </div>
            <!-- Submenu laterale che appare all'hover del logo, spostato fuori dal logo per coprire tutta la colonna -->
            <div class="logo-submenu">
                <ul>
                    <li><a href="index.html"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="register.html"><i class="fas fa-user-plus"></i> Iscriviti</a></li>
                    <li><a href="#"><i class="fas fa-envelope"></i> Messaggi</a></li>
                    <li><a href="#"><i class="fas fa-chart-bar"></i> Statistiche</a></li>
                    <li><a href="#"><i class="fas fa-user-circle"></i> Profilo</a></li>
                    <li><a href="#" id="submenu-logout"><i class="fas fa-sign-out-alt"></i> Esci</a></li>
                </ul>
            </div>
            <nav>
                <ul>
                    <li class="active"><i class="fas fa-play"></i><span>Gioca</span><div class="menu-tooltip">Gioca</div></li>
                    <li><i class="fas fa-book"></i><span>Regole</span><div class="menu-tooltip">Regole</div></li>
                    <li><i class="fas fa-trophy"></i><span>Tornei</span><div class="menu-tooltip">Tornei</div></li>
                    <li><i class="fas fa-graduation-cap"></i><span>Tutorial</span><div class="menu-tooltip">Tutorial</div></li>
                    <li><i class="fas fa-chart-line"></i><span>Statistiche</span><div class="menu-tooltip">Statistiche</div></li>
                    <li><i class="fas fa-users"></i><span>Community</span><div class="menu-tooltip">Community</div></li>
                    <li id="game-classifica-link"><i class="fas fa-medal"></i><span>Classifica</span><div class="menu-tooltip">Classifica</div></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <ul class="sidebar-icons">
                    <li id="game-user-profile">
                        <i class="fas fa-user"></i>
                        <div class="menu-tooltip" id="user-tooltip-text">Utente</div>
                    </li>
                    <li id="game-login-button">
                        <i class="fas fa-sign-in-alt"></i>
                        <div class="menu-tooltip">Accedi</div>
                    </li>
                    <li id="game-register-button">
                        <i class="fas fa-user-plus"></i>
                        <div class="menu-tooltip">Registrati</div>
                    </li>
                    <li>
                        <i class="fas fa-globe"></i>
                        <div class="menu-tooltip">Italiano</div>
                    </li>
                    <li>
                        <i class="fas fa-question-circle"></i>
                        <div class="menu-tooltip">Supporto</div>
                    </li>
                </ul>
            </div>
        </div>

        <div id="players-column">
            <div id="player1-area">
                <div class="player-header">
                    <h2>
                        <div class="player-dot white"></div>
                        <div class="player-info-box">
                            <span class="player-name">...</span> <span class="player-rating" id="player1-rating"></span>
                            <div class="player-avatar-wrapper">
                                <div class="player-avatar-container" id="player1-avatar-container"></div>
                                <div class="player-avatar-level" id="player1-avatar-level"></div>
                            </div>
                        </div>
                    </h2>
                    <div id="player1-total-timer" class="total-timer-integrated">
                        <div class="hourglass">
                            <div class="hourglass-top"></div>
                            <div class="hourglass-bottom"></div>
                        </div>
                        <div class="timer-wrapper">
                            <i class="fas fa-clock"></i>
                            <span class="total-timer-count">00:00</span>
                        </div>
                    </div>
                </div>

                <div id="player1-hand" class="hand-area">
                    <!-- Carte del giocatore 1 (bianco) verranno aggiunte qui -->
                </div>
            </div>

            <div id="player2-area">
                <div class="player-header">
                    <h2>
                        <div class="player-dot black"></div>
                        <div class="player-info-box">
                            <span class="player-name">...</span> <span class="player-rating" id="player2-rating"></span>
                            <div class="player-avatar-wrapper">
                                <div class="player-avatar-container" id="player2-avatar-container"></div>
                                <div class="player-avatar-level" id="player2-avatar-level"></div>
                            </div>
                        </div>
                    </h2>
                    <div id="player2-total-timer" class="total-timer-integrated">
                        <div class="hourglass">
                            <div class="hourglass-top"></div>
                            <div class="hourglass-bottom"></div>
                        </div>
                        <div class="timer-wrapper">
                            <i class="fas fa-clock"></i>
                            <span class="total-timer-count">00:00</span>
                        </div>
                    </div>
                </div>

                <div id="player2-hand" class="hand-area">
                    <!-- Carte del giocatore 2 (nero) verranno aggiunte qui -->
                </div>
            </div>
        </div>

        <div id="board-area">
            <div id="advantage-indicator">
                <div class="advantage-white">
                    <span class="advantage-label">G1</span>
                </div>
                <div class="advantage-black">
                    <span class="advantage-label">G2</span>
                </div>
            </div>
            <div id="game-board">
                <!-- La griglia di gioco verrà generata qui -->
            </div>

            <!-- Il mazzo è stato spostato fuori dal board-area per posizionarlo rispetto alla viewport -->

            <div id="board-sidebar">
                <!-- Tab container -->
                <div id="sidebar-tabs" class="sidebar-tabs">
                    <div class="tab active" data-tab="gioca">
                        <i class="fas fa-gamepad"></i>
                        <span class="tab-text">Gioca</span>
                    </div>
                    <div class="tab" data-tab="nuova-partita">
                        <i class="fas fa-play"></i>
                        <span class="tab-text">Nuova Partita</span>
                    </div>
                    <div class="tab" data-tab="analisi">
                        <i class="fas fa-chart-bar"></i>
                        <span class="tab-text">Analisi</span>
                    </div>
                    <div class="tab" data-tab="giocatori">
                        <i class="fas fa-users"></i>
                        <span class="tab-text">Giocatori</span>
                    </div>
                </div>

                <!-- Tab content containers -->
                <div id="sidebar-content">
                    <!-- Gioca tab content -->
                    <div id="tab-gioca" class="tab-content active">
                        <div id="game-message-container">
                            <p id="game-message"></p>
                        </div>

                        <div class="game-actions">
                            <h4><i class="fas fa-chess"></i> Azioni di Gioco</h4>
                            <div class="action-buttons">
                                <button id="draw-card-button" class="action-button">
                                    <i class="fas fa-hand-paper"></i>
                                    <span>Pesca Carta</span>
                                    <span id="deck-counter" class="deck-counter">39</span>
                                </button>
                                <button id="hint-button" class="action-button">
                                    <i class="fas fa-lightbulb"></i>
                                    <span>Suggerimento</span>
                                </button>
                            </div>
                            <!-- Il deck-area è stato spostato fuori per metterlo al centro dello schermo -->
                        </div>

                        <div class="game-status">
                            <h4><i class="fas fa-info-circle"></i> Stato Partita</h4>
                            <div class="status-info">
                                <div class="status-row">
                                    <span class="status-label">Turno:</span>
                                    <span class="status-value" id="current-turn">-</span>
                                </div>

                                <div class="status-row move-navigation">
                                    <span class="status-label">Mosse:</span>
                                    <div class="move-navigation-controls">
                                        <button id="prev-move-btn" class="move-nav-btn" title="Mossa precedente">
                                            <i class="fas fa-arrow-left"></i>
                                        </button>
                                        <span class="move-counter" id="current-move-counter">0/0</span>
                                        <button id="next-move-btn" class="move-nav-btn" title="Mossa successiva">
                                            <i class="fas fa-arrow-right"></i>
                                        </button>
                                        <button id="current-move-btn" class="move-nav-btn" title="Torna alla mossa corrente">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Opzioni di fine partita integrate nello stato partita -->
                                <div class="status-row end-game-options-row">
                                    <span class="status-label">Opzioni:</span>
                                    <div class="end-game-buttons-integrated">
                                        <button id="offer-draw-button" class="end-game-button draw-button">
                                            <i class="fas fa-handshake"></i>
                                            <span>Chiedi Patta</span>
                                        </button>
                                        <button id="resign-button" class="end-game-button resign-button">
                                            <i class="fas fa-times-circle"></i>
                                            <span>Abbandona</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sezione Skèmino notation -->
                        <div id="skemino-notation-area">
                            <!-- PSN Visualizer will be initialized here -->
                        </div>

                        <!-- Chat minimalista per il multiplayer -->
                        <div id="chat-area" class="chat-area">
                            <div class="chat-header">
                                <i class="fas fa-comments"></i> Chat Multiplayer
                            </div>
                            <div id="chat-messages" class="chat-messages"></div>
                            <div class="chat-input-container">
                                <input type="text" id="chat-input" placeholder="Invia messaggio..." maxlength="100">
                                <button id="emoji-button" class="emoji-button">
                                    <i class="fas fa-smile"></i>
                                </button>
                            </div>
                            <div id="emoji-picker" class="emoji-picker">
                                <div class="emoji-list">
                                    <span class="emoji-item">😀</span>
                                    <span class="emoji-item">😎</span>
                                    <span class="emoji-item">👍</span>
                                    <span class="emoji-item">👏</span>
                                    <span class="emoji-item">🎮</span>
                                    <span class="emoji-item">🤔</span>
                                    <span class="emoji-item">🙂</span>
                                    <span class="emoji-item">😊</span>
                                    <span class="emoji-item">👋</span>
                                    <span class="emoji-item">🏆</span>
                                    <span class="emoji-item">⭐</span>
                                    <span class="emoji-item">❤️</span>
                                </div>
                            </div>
                        </div>

                        <!-- Modali di conferma (nascosti di default) -->
                        <div id="draw-confirmation-modal" class="game-modal">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4><i class="fas fa-handshake"></i> Conferma Patta</h4>
                                </div>
                                <div class="modal-body">
                                    <p>Sei sicuro di voler proporre una patta all'avversario?</p>
                                    <p class="modal-note">La partita terminerà in parità se l'avversario accetta.</p>
                                </div>
                                <div class="modal-footer">
                                    <button id="confirm-draw" class="confirm-button">
                                        <i class="fas fa-check"></i> Conferma
                                    </button>
                                    <button id="cancel-draw" class="cancel-button">
                                        <i class="fas fa-times"></i> Annulla
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div id="resign-confirmation-modal" class="game-modal">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4><i class="fas fa-times-circle"></i> Conferma Abbandono</h4>
                                </div>
                                <div class="modal-body">
                                    <p>Sei sicuro di voler abbandonare la partita?</p>
                                    <p class="modal-note">Questa azione non può essere annullata e comporterà la sconfitta.</p>
                                </div>
                                <div class="modal-footer">
                                    <button id="confirm-resign" class="confirm-button">
                                        <i class="fas fa-check"></i> Conferma
                                    </button>
                                    <button id="cancel-resign" class="cancel-button">
                                        <i class="fas fa-times"></i> Annulla
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Nuova Partita tab content -->
                    <div id="tab-nuova-partita" class="tab-content">
                        <div id="game-message-container-nuova-partita">
                            <p id="game-message-nuova-partita"></p>
                        </div>

                        <div class="new-game-options">
                            <h4><i class="fas fa-play-circle"></i> Opzioni Nuova Partita</h4>
                            <div class="options-container">
                                <div class="option-row">
                                    <span class="option-label">Modalità:</span>
                                    <div class="option-value">
                                        <select id="game-mode-select" class="game-select">
                                            <option value="local">Locale (2 giocatori)</option>
                                            <option value="online">Online</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="option-row">
                                    <span class="option-label">Durata partita:</span>
                                    <div class="option-value">
                                        <select id="time-select" class="game-select">
                                            <option value="10min">10 minuti</option>
                                            <option value="20min">20 minuti</option>
                                            <option value="30min" selected>30 minuti</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="start-game-button-container">
                                <button id="start-new-game-button" class="start-game-button">
                                    <i class="fas fa-play"></i>
                                    <span>Inizia Nuova Partita</span>
                                </button>

                                <button id="challenge-friend-button" class="secondary-game-button">
                                    <i class="fas fa-user-friends"></i>
                                    <span>Sfida un Amico</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Analisi tab content -->
                    <div id="tab-analisi" class="tab-content">
                        <div class="analisi-container">
                            <h4><i class="fas fa-chart-line"></i> Analisi Partita</h4>
                            <div class="analisi-stats">
                                <div class="stat-row">
                                    <span class="stat-label">Mosse giocate:</span>
                                    <span class="stat-value" id="mosse-giocate">0</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-label">Carte piazzate:</span>
                                    <span class="stat-value" id="carte-piazzate">0</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-label">Vertici conquistati:</span>
                                    <span class="stat-value" id="vertici-conquistati">0</span>
                                </div>
                            </div>
                            <div class="analisi-grafico">
                                <h5>Distribuzione carte</h5>
                                <div class="grafico-placeholder">
                                    <i class="fas fa-chart-pie"></i>
                                    <p>Grafico disponibile a fine partita</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Giocatori tab content -->
                    <div id="tab-giocatori" class="tab-content">
                        <div class="giocatori-container">
                            <h4><i class="fas fa-user-circle"></i> Profili Giocatori</h4>

                            <div class="giocatore-profilo" id="profilo-giocatore1">
                                <div class="profilo-header">
                                    <div class="profilo-avatar" id="profilo-avatar1"></div>
                                    <div class="profilo-info">
                                        <h5 id="profilo-nome1">...</h5>
                                        <span class="profilo-livello" id="profilo-livello1">Principiante</span>
                                    </div>
                                </div>
                                <div class="profilo-stats">
                                    <div class="stat-row">
                                        <span class="stat-label">Vittorie:</span>
                                        <span class="stat-value" id="vittorie1">0</span>
                                    </div>
                                    <div class="stat-row">
                                        <span class="stat-label">Sconfitte:</span>
                                        <span class="stat-value" id="sconfitte1">0</span>
                                    </div>
                                    <div class="stat-row">
                                        <span class="stat-label">Rating:</span>
                                        <span class="stat-value" id="rating1">1000</span>
                                    </div>
                                </div>
                            </div>

                            <div class="giocatore-profilo" id="profilo-giocatore2">
                                <div class="profilo-header">
                                    <div class="profilo-avatar" id="profilo-avatar2"></div>
                                    <div class="profilo-info">
                                        <h5 id="profilo-nome2">...</h5>
                                        <span class="profilo-livello" id="profilo-livello2">Principiante</span>
                                    </div>
                                </div>
                                <div class="profilo-stats">
                                    <div class="stat-row">
                                        <span class="stat-label">Vittorie:</span>
                                        <span class="stat-value" id="vittorie2">0</span>
                                    </div>
                                    <div class="stat-row">
                                        <span class="stat-label">Sconfitte:</span>
                                        <span class="stat-value" id="sconfitte2">0</span>
                                    </div>
                                    <div class="stat-row">
                                        <span class="stat-label">Rating:</span>
                                        <span class="stat-value" id="rating2">1000</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Area log rimossa -->
    </div>

    <!-- Barra di scorrimento personalizzata -->
    <div class="custom-scrollbar">
        <div class="scrollbar-thumb"></div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="auth.js"></script>
    <script src="js/redirect.js"></script>
    <script src="script.js"></script>
    <script src="js/multiplayer.js"></script>
    <script src="js/psn-unified.js"></script>
    <script src="js/page-transitions.js"></script>
    <script src="js/login.js"></script>
    <script src="js/logout-handler.js"></script>
    <script src="js/mobile-menu.js"></script>
    <script src="js/page-visibility-handler.js"></script>
    <script src="js/deck-visibility-fix.js"></script>
    <script src="js/animation-cleanup.js"></script>
    <script src="js/online-play-enhancer.js"></script>
    <script src="js/home-effects.js"></script>

    <!-- Script per gestire la barra di scorrimento personalizzata -->
    <script src="js/custom-scrollbar.js"></script>
</body>
</html>