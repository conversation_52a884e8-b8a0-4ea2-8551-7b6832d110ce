/* Mobile Menu Styles for Skèmino */

/* Mobile header styles */
.mobile-header {
    display: none !important; /* Nascosto di default, visibile solo su mobile */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: rgba(10, 20, 35, 0.95);
    color: #e0f0ff;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    padding: 0 15px;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
}

.mobile-logo {
    display: flex;
    align-items: center;
    height: 100%;
}

.mobile-logo img {
    height: 40px;
    width: auto;
}

.hamburger-menu {
    background: none;
    border: none;
    color: #e0f0ff;
    font-size: 24px;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
    position: relative;
    right: 5px;
    margin-left: auto;
}

.hamburger-menu:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Mobile menu overlay styles */
.mobile-menu-overlay {
    display: none !important; /* Nascosto di default, visibile solo su mobile */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1001;
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
}

.mobile-menu-overlay.active {
    opacity: 1;
    pointer-events: auto;
}

.mobile-menu {
    position: fixed;
    top: 0;
    right: -280px;
    width: 280px;
    height: 100%;
    background-color: rgba(10, 20, 35, 0.95);
    color: #e0f0ff;
    z-index: 1002;
    padding: 20px 0;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
    overflow-y: auto;
    transition: right 0.3s ease;
    display: none !important; /* Nascosto di default, visibile solo su mobile */
    flex-direction: column;
    visibility: visible !important;
}

.mobile-menu.active {
    right: 0 !important;
    opacity: 1 !important;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    margin-bottom: 20px;
}

.close-menu {
    background: none;
    border: none;
    color: #e0f0ff;
    font-size: 24px;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.close-menu:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Mobile navigation styles */
.mobile-nav {
    flex: 1;
}

.mobile-nav ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.mobile-nav li {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s;
    color: #c0d0ff;
}

.mobile-nav li:hover {
    background-color: rgba(30, 60, 120, 0.3);
    color: #e0f0ff;
}

.mobile-nav li.active {
    background-color: rgba(30, 60, 120, 0.5);
    border-left: 4px solid rgba(50, 220, 100, 0.9);
    color: #e0f0ff;
}

.mobile-nav li i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Mobile menu footer styles */
.mobile-menu-footer {
    padding: 20px;
    border-top: 1px solid rgba(100, 150, 255, 0.2);
    margin-top: 20px;
}

#mobile-auth-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

#mobile-auth-buttons .auth-btn {
    width: 100%;
}

.mobile-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
}

.mobile-controls button {
    width: 100%;
    background-color: transparent;
    color: #c0d0ff;
    border: 1px solid rgba(100, 150, 255, 0.3);
    border-radius: 5px;
    padding: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s;
}

.mobile-controls button:hover {
    background-color: rgba(30, 60, 120, 0.3);
    color: #e0f0ff;
}

/* Media queries */
@media (max-width: 992px) {
    #homepage {
        padding-left: 0 !important;
        padding-top: 60px !important;
    }

    .sidebar {
        display: none !important;
    }

    .mobile-header {
        display: flex !important;
    }

    /* Allow menu overlay to be controlled by JavaScript */
    .mobile-menu-overlay {
        display: block !important;
    }

    /* Ensure the menu is visible */
    .mobile-menu {
        display: flex !important;
    }
}

/* Sempre visibile quando attivo, indipendentemente dalla dimensione dello schermo */
.mobile-menu.active {
    right: 0 !important;
    display: flex !important;
}

@media (max-width: 992px) {
    .main-content {
        width: 100%;
        padding: 15px;
        box-sizing: border-box;
    }
}

/* Add extra padding for smaller screens */
@media (max-width: 768px) {
    .main-content {
        padding: 10px;
    }

    /* Adjust spacing for smaller mobile screens */
    .hero-section {
        flex-direction: column;
        gap: 20px;
        margin-top: 15px; /* Aggiunge spazio sopra la hero section su mobile */
        width: 100%; /* Occupa tutta la larghezza disponibile */
        border-radius: 15px; /* Bordo più arrotondato su mobile */
    }

    /* Migliora la visualizzazione del tabellone su mobile */
    .board-preview {
        width: 80% !important; /* Forza la larghezza su mobile */
        margin-top: 10px;
        margin-bottom: 15px;
        height: auto !important; /* Forza l'altezza automatica su mobile */
    }

    /* Assicura che l'immagine del tabellone sia sempre visualizzata interamente */
    .tabellone-image {
        width: 100% !important;
        height: auto !important;
        padding: 0 !important;
        max-height: none !important; /* Rimuove il limite di altezza massima */
    }

    .tabellone-img {
        width: auto !important; /* Larghezza automatica per mantenere le proporzioni */
        height: auto !important; /* Altezza automatica per mantenere le proporzioni */
        object-fit: contain !important; /* Forza object-fit: contain su mobile */
        max-width: 100% !important;
        max-height: none !important; /* Rimuove il limite di altezza massima */
        display: block !important; /* Assicura che l'immagine sia visualizzata come blocco */
    }

    /* Migliora la visualizzazione del contenuto su mobile */
    .hero-content {
        width: 100% !important; /* Forza la larghezza su mobile */
        padding: 15px !important;
        display: flex !important; /* Forza display flex */
        flex-direction: column !important;
        justify-content: center !important; /* Forza il centering verticale */
        align-items: center !important; /* Forza il centering orizzontale */
        height: auto !important; /* Altezza automatica su mobile */
    }

    /* Migliora la visualizzazione dei pulsanti su mobile */
    .play-buttons {
        width: 100%;
        max-width: 100%;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .mobile-header {
        height: 50px;
        padding: 0 10px;
    }

    .mobile-logo img {
        height: 32px;
    }

    .hamburger-menu {
        font-size: 20px;
        right: 0;
        margin-right: 5px;
    }
}

/* Body class to disable scrolling when menu is open */
body.menu-open {
    overflow: hidden;
}

/* Schermi molto piccoli (sotto i 360px) */
@media (max-width: 360px) {
    .hero-section {
        padding: 10px;
        border-radius: 10px;
    }

    .board-preview {
        width: 90% !important; /* Aumenta la larghezza su schermi molto piccoli */
        max-width: 150px;
        padding: 5px;
        height: auto !important;
    }

    /* Assicura che l'immagine del tabellone sia sempre visualizzata interamente */
    .tabellone-image {
        width: 100% !important;
        height: auto !important;
        padding: 0 !important;
        max-height: none !important; /* Rimuove il limite di altezza massima */
    }

    .tabellone-img {
        width: auto !important; /* Larghezza automatica per mantenere le proporzioni */
        height: auto !important; /* Altezza automatica per mantenere le proporzioni */
        object-fit: contain !important;
        max-width: 100% !important;
        max-height: none !important; /* Rimuove il limite di altezza massima */
        border-width: 2px !important; /* Bordo più sottile su schermi molto piccoli */
        display: block !important; /* Assicura che l'immagine sia visualizzata come blocco */
    }

    .hero-content {
        width: 100% !important;
        padding: 10px 5px !important;
    }

    .hero-content h1 {
        font-size: 1.3em;
    }

    .hero-content h2 {
        font-size: 0.8em;
        margin-bottom: 15px;
    }

    .play-buttons .primary-btn {
        padding: 10px;
        font-size: 0.75em;
    }
}