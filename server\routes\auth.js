const express = require('express');
const authController = require('../controllers/authController');
const authMiddleware = require('../middleware/auth');
const { loginRateLimit } = require('../middleware/rateLimit');
const path = require('path');

const router = express.Router();

// Test endpoint
router.get('/test', (req, res) => {
    res.status(200).json({ success: true, message: 'API funzionante' });
});

// Debug registration endpoint
router.post('/debug-register', (req, res) => {
    try {
        console.log('Debug registration request received');
        console.log('Headers:', req.headers);
        console.log('Body:', req.body);

        // Return all the information we received
        res.status(200).json({
            success: true,
            message: 'Debug info received',
            headers: req.headers,
            body: req.body,
            bodyType: typeof req.body,
            hasBody: !!req.body,
            bodyKeys: req.body ? Object.keys(req.body) : []
        });
    } catch (error) {
        console.error('Debug registration error:', error);
        res.status(500).json({
            success: false,
            message: 'Error in debug endpoint',
            error: error.message,
            stack: error.stack
        });
    }
});

// Direct registration endpoint (bypasses most checks)
router.post('/direct-register', async (req, res) => {
    try {
        console.log('Direct registration request received');

        const { username, email, password } = req.body;

        if (!username || !email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Username, email, and password are required'
            });
        }

        // Import required modules
        const db = require('../database/db');
        const security = require('../utils/security');
        const User = require('../database/models/User');

        // Generate salt and hash password
        const salt = await security.generateSalt();
        const hashedPassword = await security.hashPassword(password, salt);

        // Generate activation token
        const activationToken = await security.generateToken();

        try {
            // Insert directly into database
            const result = await db.run(
                `INSERT INTO users (username, email, password, salt, activation_token, is_active)
                 VALUES (?, ?, ?, ?, ?, 1)`,
                [username, email, hashedPassword, salt, activationToken]
            );

            console.log('Direct user creation result:', result);

            res.status(201).json({
                success: true,
                message: 'User created successfully and automatically activated',
                userId: result.lastID,
                username,
                email
            });
        } catch (dbError) {
            console.error('Database error during direct registration:', dbError);

            // Try with in-memory database as fallback
            const users = [];
            const userId = 1;

            users.push({
                id: userId,
                username,
                email,
                password: hashedPassword,
                salt,
                is_active: 1,
                created_at: new Date().toISOString()
            });

            console.log('Created in-memory user:', users[0]);

            res.status(201).json({
                success: true,
                message: 'User created in memory (fallback mode)',
                userId,
                username,
                email,
                inMemory: true
            });
        }
    } catch (error) {
        console.error('Direct registration error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error during direct registration',
            error: error.message,
            stack: error.stack
        });
    }
});

// Route per visualizzare il form di registrazione
router.get('/register', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/register.html'));
});

// Public routes
router.post('/register', authController.register);
router.post('/login', loginRateLimit, authController.login);
router.get('/logout', authController.logout);
router.post('/forgot-password', authController.requestPasswordReset);
router.post('/reset-password/:token', authController.resetPassword);
router.get('/activate/:token', authController.activate);
router.get('/check-username', authController.checkUsername);
router.post('/refresh-token', authController.refreshToken);

// Protected routes
router.get('/me', authMiddleware, authController.getCurrentUser);

module.exports = router;
