// Mobile Menu Functionality for Skèmino
document.addEventListener('DOMContentLoaded', function() {
    console.log('Mobile menu script loaded');
    
    // Get DOM elements
    const hamburgerButton = document.querySelector('.hamburger-menu');
    const closeButton = document.querySelector('.close-menu');
    const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');
    const mobileMenu = document.querySelector('.mobile-menu');
    const body = document.body;
    
    console.log('Hamburger button:', hamburgerButton);
    console.log('Close button:', closeButton);
    console.log('Mobile menu overlay:', mobileMenuOverlay);
    console.log('Mobile menu:', mobileMenu);
    
    // Assicuriamoci che elementi mobile siano visibili solo su mobile/tablet
    function updateMobileElements() {
        const isMobile = window.innerWidth <= 992;
        const mobileHeader = document.querySelector('.mobile-header');
        const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        if (mobileHeader) {
            mobileHeader.style.display = isMobile ? 'flex' : 'none';
        }
        
        if (mobileMenuOverlay) {
            mobileMenuOverlay.style.display = isMobile ? 'block' : 'none';
        }
        
        if (mobileMenu) {
            // Manteniamo il menu fuori schermo ma visibile per permettere la transizione
            mobileMenu.style.display = isMobile ? 'flex' : 'none';
        }
    }
    
    // Esegui immediatamente
    updateMobileElements();
    
    // Aggiorna quando la finestra cambia dimensione
    window.addEventListener('resize', updateMobileElements);

    // Mobile menu items
    const mobileMenuItems = document.querySelectorAll('.mobile-nav li');
    const mobileLoginButton = document.getElementById('mobile-login-button');
    const mobileRegisterButton = document.getElementById('mobile-register-button');
    const mobileLogoutButton = document.getElementById('mobile-logout-button');
    const mobileClassificaLink = document.getElementById('mobile-classifica-link');

    // Function to open the mobile menu
    function openMobileMenu() {
        console.log('Opening mobile menu');
        mobileMenuOverlay.classList.add('active');
        mobileMenu.classList.add('active');
        body.classList.add('menu-open');
        console.log('Menu active states:', {
            overlay: mobileMenuOverlay.classList.contains('active'),
            menu: mobileMenu.classList.contains('active')
        });
    }

    // Function to close the mobile menu
    function closeMobileMenu() {
        console.log('Closing mobile menu');
        mobileMenuOverlay.classList.remove('active');
        mobileMenu.classList.remove('active');
        body.classList.remove('menu-open');
    }

    // Event listeners
    if (hamburgerButton) {
        console.log('Adding click event to hamburger button');
        hamburgerButton.addEventListener('click', function(e) {
            console.log('Hamburger button clicked');
            e.preventDefault();
            openMobileMenu();
        });
    }
    
    if (closeButton) {
        console.log('Adding click event to close button');
        closeButton.addEventListener('click', function(e) {
            console.log('Close button clicked');
            e.preventDefault();
            closeMobileMenu();
        });
    }
    
    if (mobileMenuOverlay) {
        mobileMenuOverlay.addEventListener('click', function(e) {
            // Close the menu only if the overlay itself is clicked
            if (e.target === mobileMenuOverlay) {
                closeMobileMenu();
            }
        });
    }

    // Menu items functionality - make them behave the same as desktop menu items
    if (mobileMenuItems) {
        mobileMenuItems.forEach(function(item) {
            item.addEventListener('click', function() {
                // Get the corresponding desktop item based on the icon and text
                const itemText = this.textContent.trim();
                const desktopItems = document.querySelectorAll('.sidebar li');
                
                desktopItems.forEach(function(desktopItem) {
                    if (desktopItem.textContent.trim() === itemText) {
                        // Trigger the same event on the desktop item
                        desktopItem.click();
                    }
                });
                
                // Close the mobile menu
                closeMobileMenu();
            });
        });
    }

    // Auth buttons in mobile menu
    if (mobileLoginButton) {
        mobileLoginButton.addEventListener('click', function() {
            // Trigger the desktop login button
            const desktopLoginButton = document.getElementById('login-button');
            if (desktopLoginButton) {
                desktopLoginButton.click();
            }
            closeMobileMenu();
        });
    }

    if (mobileRegisterButton) {
        mobileRegisterButton.addEventListener('click', function() {
            // Trigger the desktop register button
            const desktopRegisterButton = document.getElementById('register-button');
            if (desktopRegisterButton) {
                desktopRegisterButton.click();
            }
            closeMobileMenu();
        });
    }

    if (mobileLogoutButton) {
        mobileLogoutButton.addEventListener('click', function() {
            // Trigger the desktop logout button
            const desktopLogoutButton = document.getElementById('logout-button');
            if (desktopLogoutButton) {
                desktopLogoutButton.click();
            }
            closeMobileMenu();
        });
    }

    if (mobileClassificaLink) {
        mobileClassificaLink.addEventListener('click', function() {
            // Trigger the desktop classifica link
            const desktopClassificaLink = document.getElementById('classifica-link');
            if (desktopClassificaLink) {
                desktopClassificaLink.click();
            }
            closeMobileMenu();
        });
    }

    // Sync login state between desktop and mobile menu
    function syncAuthState() {
        // Desktop elements
        const authButtons = document.getElementById('auth-buttons');
        const userProfile = document.getElementById('user-profile');
        const usernameDisplay = document.getElementById('username-display');
        const userRank = document.getElementById('user-rank');
        const fixAuthButton = document.getElementById('fix-auth-button');
        
        // Mobile elements
        const mobileAuthButtons = document.getElementById('mobile-auth-buttons');
        const mobileUserProfile = document.getElementById('mobile-user-profile');
        const mobileUsernameDisplay = document.getElementById('mobile-username-display');
        const mobileUserRank = document.getElementById('mobile-user-rank');
        const mobileFixAuthButton = document.getElementById('mobile-fix-auth-button');
        
        // Sync display state
        if (authButtons && mobileAuthButtons) {
            mobileAuthButtons.style.display = authButtons.style.display;
        }
        
        if (userProfile && mobileUserProfile) {
            mobileUserProfile.style.display = userProfile.style.display;
        }
        
        // Sync content
        if (usernameDisplay && mobileUsernameDisplay) {
            mobileUsernameDisplay.textContent = usernameDisplay.textContent;
        }
        
        if (userRank && mobileUserRank) {
            mobileUserRank.textContent = userRank.textContent;
        }
        
        if (fixAuthButton && mobileFixAuthButton) {
            mobileFixAuthButton.style.display = fixAuthButton.style.display;
        }
    }

    // Initial sync of auth state
    syncAuthState();

    // Observer to detect changes in auth state
    const authObserver = new MutationObserver(syncAuthState);
    const authObserverConfig = { attributes: true, childList: true, subtree: true };
    
    // Observe desktop auth elements for changes
    const desktopAuthContainer = document.querySelector('.sidebar-footer');
    if (desktopAuthContainer) {
        authObserver.observe(desktopAuthContainer, authObserverConfig);
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        // Update mobile element visibility
        updateMobileElements();
        
        // If window width is greater than tablet breakpoint, close mobile menu
        if (window.innerWidth > 992 && mobileMenu && mobileMenu.classList.contains('active')) {
            closeMobileMenu();
        }
    });

    // Handle escape key to close menu
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && mobileMenu && mobileMenu.classList.contains('active')) {
            closeMobileMenu();
        }
    });
});