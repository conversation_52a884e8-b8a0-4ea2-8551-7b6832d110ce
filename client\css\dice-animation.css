/* Dice Animation Styles */

/* Area contenuta per animazione dadi - ora relativa al tabellone */
.dice-animation-area[data-hidden="true"] {
    display: none !important;
}

.dice-animation-area {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1100;
    background: rgba(15, 30, 48, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 2px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.4s ease-out;
    overflow: hidden;
}

.dice-animation-area.fade-in {
    opacity: 1;
}

.dice-animation-area.fade-out {
    opacity: 0;
}

/* Overlay scuro NON UTILIZZATO - ora l'animazione è circoscritta */
.dice-animation-overlay[data-hidden="true"] {
    display: none !important;
}

.dice-animation-overlay {
    display: none !important;
}

.dice-animation-container {
    text-align: center;
    padding: 40px 20px;
    max-width: 600px;
    margin: 0 auto;
}

.dice-animation-container h3 {
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 30px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dice-animation-container h2 {
    font-size: 2.5rem;
    color: #fff;
    margin-bottom: 30px;
    animation: fadeIn 1s ease-out;
}

.dice-area {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 60px;
    margin: 40px 0;
    perspective: 1200px;
    position: relative;
    z-index: 1;
}

.dice {
    position: relative;
    width: 100px;
    height: 100px;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.dice.rolling {
    animation: roll 1.5s ease-out;
}

@keyframes roll {
    0% {
        transform: rotateX(0deg) rotateY(0deg);
    }
    100% {
        transform: rotateX(720deg) rotateY(720deg);
    }
}

.dice-face {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, #ffffff, #e6e6e6);
    border: 2px solid #333;
    border-radius: 12px;
    display: grid;
    place-items: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    backface-visibility: hidden;
}

/* Dice dots arrangement */
.dice-face.face-1 {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
}

.dice-face.face-2 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
}

.dice-face.face-2 .dice-dot:nth-child(1) {
    justify-self: start;
    align-self: start;
    margin: 15px;
}

.dice-face.face-2 .dice-dot:nth-child(2) {
    justify-self: end;
    align-self: end;
    margin: 15px;
}

.dice-face.face-3 {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
}

.dice-face.face-3 .dice-dot:nth-child(1) {
    grid-column: 1;
    grid-row: 1;
    margin: 10px;
}

.dice-face.face-3 .dice-dot:nth-child(2) {
    grid-column: 2;
    grid-row: 2;
}

.dice-face.face-3 .dice-dot:nth-child(3) {
    grid-column: 3;
    grid-row: 3;
    margin: 10px;
}

.dice-face.face-4 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 20px;
    padding: 15px;
}

.dice-face.face-5 {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
}

.dice-face.face-5 .dice-dot:nth-child(1) {
    grid-column: 1;
    grid-row: 1;
    margin: 10px;
}

.dice-face.face-5 .dice-dot:nth-child(2) {
    grid-column: 3;
    grid-row: 1;
    margin: 10px;
}

.dice-face.face-5 .dice-dot:nth-child(3) {
    grid-column: 2;
    grid-row: 2;
}

.dice-face.face-5 .dice-dot:nth-child(4) {
    grid-column: 1;
    grid-row: 3;
    margin: 10px;
}

.dice-face.face-5 .dice-dot:nth-child(5) {
    grid-column: 3;
    grid-row: 3;
    margin: 10px;
}

.dice-face.face-6 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    gap: 10px;
    padding: 15px;
}

.dice-dot {
    width: 16px;
    height: 16px;
    background: #333;
    border-radius: 50%;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 3D positioning for dice faces */
.dice.show-face-1 .face-1 { transform: rotateY(0deg) translateZ(50px); }
.dice.show-face-2 .face-2 { transform: rotateY(90deg) translateZ(50px); }
.dice.show-face-3 .face-3 { transform: rotateY(180deg) translateZ(50px); }
.dice.show-face-4 .face-4 { transform: rotateY(-90deg) translateZ(50px); }
.dice.show-face-5 .face-5 { transform: rotateX(90deg) translateZ(50px); }
.dice.show-face-6 .face-6 { transform: rotateX(-90deg) translateZ(50px); }

/* Result text */
.dice-result-text {
    font-size: 1.4rem;
    color: rgba(255, 255, 255, 0.95);
    margin-top: 40px;
    opacity: 0;
    text-align: center;
    line-height: 1.8;
    max-width: 600px;
    animation: fadeIn 1s ease-out 0.5s forwards;
    background: rgba(255, 255, 255, 0.05);
    padding: 20px 30px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dice-result-text ul {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.dice-result-text li {
    margin: 12px 0;
    padding-left: 30px;
    position: relative;
    font-weight: 500;
}

.dice-result-text li::before {
    content: "▶";
    color: rgba(100, 200, 255, 0.9);
    font-weight: bold;
    position: absolute;
    left: 0;
    font-size: 0.8em;
    top: 3px;
}

.dice-result-text li:first-child {
    color: #60a5fa;
    font-size: 1.5rem;
    font-weight: 700;
}

.dice-result-text li:last-child {
    color: #fbbf24;
    font-weight: 600;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Setup animation container - posizionamento aggressivo fixed a schermo intero */
#setup-animation {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(23, 42, 69, 0.95) 0%, rgba(15, 30, 48, 0.95) 100%) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    z-index: 9999 !important; /* Z-index massimo possibile */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: hidden !important;
    opacity: 0 !important;
    transition: opacity 0.5s ease !important;
    /* Dimensioni forzate al 100% */
    width: 100% !important;
    height: 100% !important;
    /* Styling */
    border: 4px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.5) !important;
    /* Assicura visibilità */
    visibility: visible !important;
    transform: none !important;
    pointer-events: all !important; /* Cattura tutti gli eventi */
}

#setup-animation.fade-in {
    opacity: 1 !important;
}

#setup-animation.fade-out {
    opacity: 0 !important;
}

/* Enhanced dice with more realistic styling */
.dice {
    animation: float 2s ease-in-out infinite;
    position: relative;
}

/* Stili per i diversi tipi di dadi */
.cube {
    width: 120px;
    height: 120px;
    position: relative;
    transform-style: preserve-3d;
    transform: translateZ(-60px);
    transition: transform 1s cubic-bezier(0.4, 0.0, 0.2, 1);
    will-change: transform;
    font-family: 'Arial', sans-serif;
}

/* Assicurati che tutti i cubi abbiano la stessa dimensione */
.number-cube,
.letter-cube,
.color-cube {
    width: 120px;
    height: 120px;
}

/* Reset specifico per assicurare allineamento uniforme */
.cube {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.scene {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Animazione durante il rotolamento */
.cube.rolling {
    animation: roll 2s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.cube__face {
    position: absolute;
    width: 120px;
    height: 120px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 12px;
    line-height: 120px;
    font-size: 64px;
    font-weight: 700;
    color: white;
    text-align: center;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%);
    backface-visibility: visible;
    user-select: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

.number-cube .cube__face {
    background: linear-gradient(145deg, #2563eb 0%, #1e40af 100%);
    box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.3), 
                inset 0 -2px 4px rgba(0, 0, 0, 0.3),
                0 8px 25px rgba(37, 99, 235, 0.4);
}

.letter-cube .cube__face {
    background: linear-gradient(145deg, #dc2626 0%, #991b1b 100%);
    box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.3), 
                inset 0 -2px 4px rgba(0, 0, 0, 0.3),
                0 8px 25px rgba(220, 38, 38, 0.4);
}

.color-cube .cube__face {
    background: linear-gradient(145deg, #16a34a 0%, #15803d 100%);
    box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.3), 
                inset 0 -2px 4px rgba(0, 0, 0, 0.3),
                0 8px 25px rgba(22, 163, 74, 0.4);
}

.color-cube .cube__face.black {
    background: linear-gradient(145deg, #374151 0%, #1f2937 100%);
    color: white;
    box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.2), 
                inset 0 -2px 4px rgba(0, 0, 0, 0.5),
                0 8px 25px rgba(31, 41, 55, 0.6);
}

/* Stili specifici per il testo G1/G2 */
.color-cube .cube__face .g1,
.color-cube .cube__face .g2 {
    font-size: 64px !important;
    font-weight: 700 !important;
    color: inherit;
    text-shadow: inherit;
    display: inline;
    /* Nessun padding o margin extra */
    padding: 0 !important;
    margin: 0 !important;
    line-height: 1 !important;
    /* Reset di tutti i possibili stili ereditati */
    transform: none !important;
    transition: none !important;
    position: static !important;
    width: auto !important;
    height: auto !important;
    /* Impedisci qualsiasi ridimensionamento */
    zoom: 1 !important;
    max-width: none !important;
    max-height: none !important;
    min-width: auto !important;
    min-height: auto !important;
}

/* Posizionamento 3D delle facce */
.cube__face--front { transform: rotateY(0deg) translateZ(60px); }
.cube__face--right { transform: rotateY(90deg) translateZ(60px); }
.cube__face--back { transform: rotateY(180deg) translateZ(60px); }
.cube__face--left { transform: rotateY(-90deg) translateZ(60px); }
.cube__face--top { transform: rotateX(90deg) translateZ(60px); }
.cube__face--bottom { transform: rotateX(-90deg) translateZ(60px); }

/* Effetto sulla faccia visibile */
.cube__face::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.2) 0%, 
        transparent 60%, 
        rgba(0, 0, 0, 0.1) 100%);
    pointer-events: none;
}

/* Animazione di rotazione */
@keyframes roll {
    0% { 
        transform: translateZ(-60px) rotateX(0deg) rotateY(0deg); 
    }
    25% {
        transform: translateZ(-60px) rotateX(360deg) rotateY(180deg);
    }
    50% {
        transform: translateZ(-60px) rotateX(720deg) rotateY(360deg);
    }
    75% {
        transform: translateZ(-60px) rotateX(900deg) rotateY(540deg);
    }
    100% { 
        transform: translateZ(-60px) rotateX(1080deg) rotateY(720deg); 
    }
}

.scene {
    width: 120px;
    height: 120px;
    position: relative;
    perspective: 800px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.column {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    height: 100%;
    justify-content: flex-end;
}

.dice-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 700;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    backdrop-filter: blur(10px);
}

.dice-container {
    display: flex;
    gap: 60px;
    align-items: flex-end;
    justify-content: center;
    margin: 40px 0;
    padding: 20px;
    height: 200px;
}

/* Stati speciali per i dadi */
.cube.show-num-1 { transform: translateZ(-60px) rotateY(0deg); }
.cube.show-num-2 { transform: translateZ(-60px) rotateY(180deg); }
.cube.show-num-3 { transform: translateZ(-60px) rotateY(-90deg); }
.cube.show-num-4 { transform: translateZ(-60px) rotateY(90deg); }
.cube.show-num-5 { transform: translateZ(-60px) rotateX(-90deg); }
.cube.show-num-6 { transform: translateZ(-60px) rotateX(90deg); }

.cube.show-letter-A { transform: translateZ(-60px) rotateY(0deg); }
.cube.show-letter-B { transform: translateZ(-60px) rotateY(180deg); }
.cube.show-letter-C { transform: translateZ(-60px) rotateY(-90deg); }
.cube.show-letter-D { transform: translateZ(-60px) rotateY(90deg); }
.cube.show-letter-E { transform: translateZ(-60px) rotateX(-90deg); }
.cube.show-letter-F { transform: translateZ(-60px) rotateX(90deg); }

.cube.show-color-white { transform: translateZ(-60px) rotateY(0deg); }
.cube.show-color-black { transform: translateZ(-60px) rotateY(180deg); }

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Effetto hover disabilitato durante il rolling */
.cube:not(.rolling):hover {
    transform: translateZ(-60px) scale(1.05);
    transition: transform 0.3s ease;
}

/* Add glow effect to dice */
.scene::after {
    content: '';
    position: absolute;
    width: 130%;
    height: 130%;
    top: -15%;
    left: -15%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 60%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: -1;
}

.cube.rolling ~ .scene::after {
    opacity: 1;
    animation: pulse 1.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.1;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* Animazione nomi giocatori */
.player-names-animation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2200; /* Aumentato per essere sopra setup-animation */
    pointer-events: none;
    overflow: hidden;
}

.player-name-flying {
    position: absolute !important;
    font-size: 3rem !important;
    font-weight: 900 !important;
    color: white !important;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.9), 0 0 30px rgba(255, 255, 255, 0.3) !important;
    text-transform: uppercase !important;
    letter-spacing: 4px !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    transform: scale(1.2) !important;
    animation: playerNameEntry 2s ease-out forwards !important;
    max-width: 80% !important;
    text-align: center !important;
    z-index: 99999 !important;
    visibility: visible !important;
}

.player-name-flying.player1 {
    top: -50px;
    left: 50%;
    transform: translateX(-50%) scale(1.2);
    color: #60a5fa;
    text-shadow: 0 0 20px rgba(96, 165, 250, 0.8), 0 4px 8px rgba(0, 0, 0, 0.8);
}

.player-name-flying.player2 {
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%) scale(1.2);
    color: #fbbf24;
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.8), 0 4px 8px rgba(0, 0, 0, 0.8);
}

@keyframes playerNameEntry {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(0) scale(1.2);
    }
    30% {
        opacity: 1;
        transform: translateX(-50%) translateY(80px) scale(1.1);
    }
    60% {
        opacity: 1;
        transform: translateX(-50%) translateY(120px) scale(1);
    }
    80% {
        opacity: 1;
        transform: translateX(-50%) translateY(140px) scale(0.9);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(160px) scale(0.8);
    }
}

.player-name-flying.player2 {
    animation: playerNameEntryBottom 2s ease-out forwards;
}

@keyframes playerNameEntryBottom {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(0) scale(1.2);
    }
    30% {
        opacity: 1;
        transform: translateX(-50%) translateY(-80px) scale(1.1);
    }
    60% {
        opacity: 1;
        transform: translateX(-50%) translateY(-120px) scale(1);
    }
    80% {
        opacity: 1;
        transform: translateX(-50%) translateY(-140px) scale(0.9);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-160px) scale(0.8);
    }
}

/* Animazione wow centrale */
.setup-center-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2300; /* Aumentato per essere sopra setup-animation e player-names */
    text-align: center;
    opacity: 0;
    animation: setupCenterAnimation 3s ease-in-out forwards;
    animation-delay: 1s;
    pointer-events: none; /* Permette i click sugli elementi sottostanti */
}

.setup-center-text h2 {
    font-size: 2.2rem;
    font-weight: 800;
    color: white;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.8), 0 4px 8px rgba(0, 0, 0, 0.8);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 3px;
}

.setup-center-text p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin: 15px 0 0 0;
    letter-spacing: 2px;
}

@keyframes setupCenterAnimation {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    20% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1);
    }
    40% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    80% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
}
